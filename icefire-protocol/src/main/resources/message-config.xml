<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE xml>

<messages>
    <message type="1" name="CgLogin" />
    <message type="2" name="CgEnterMap" />
    <message type="3" name="GcEnterMap" />
    <message type="4" name="CgSynchronizeTime" />
    <message type="5" name="GcSynchronizeTime" />
    <message type="6" name="GcCloseClient" />
    <message type="7" name="CgReconnect" />
    <message type="8" name="GcReconnect" />
    <message type="9" name="CgClose" />
    <message type="10" name="GcEnterGamingStatus" />
    <message type="11" name="GcGameErrorRet" />
    <message type="12" name="GcLogin" />
    <message type="13" name="CgVisitorLogin" />
    <message type="100" name="GcPlayerInfo" />
    <message type="101" name="GcPlayerPropertyUpdated" />
    <message type="102" name="CgPlayerNameExist" />
    <message type="103" name="GcPlayerNameExist" />
    <message type="104" name="CgPlayerNameChange" />
    <message type="105" name="GcPlayerNameChange" />
    <message type="106" name="GcPlayerInvalidChar" />
    <message type="107" name="GcUtcNextZeroClockTimestamp" />
    <message type="111" name="CgPlayerCountryChange" />
    <message type="112" name="GcPlayerCountryChange" />
    <message type="115" name="GcPlayerCommonSettingInfo" />
    <message type="116" name="CgPlayerCommonSettingChange" />
    <message type="117" name="CgPlayerChangeCamp" />
    <message type="118" name="GcPlayerChangeCamp" />
    <message type="119" name="GcPlayerVipInfo" />
    <message type="120" name="CgPlayerVipLoginRewards" />
    <message type="121" name="CgPlayerVipLevelRewards" />
    <message type="122" name="GcPlayerVipLevelRewards" />
    <message type="127" name="CgPlayerGenderChange" />
    <message type="128" name="GcPlayerGenderChange" />
    <message type="129" name="GcGamePushInfo" />
    <message type="130" name="CgSyncPlayerCurrency" />
    <message type="131" name="CgExchangeGiftCode" />
    <message type="132" name="GcExchangeGiftCode" />
    <message type="133" name="GcUpdateCanAttendAllianceTime" />
    <message type="134" name="GcPlayerLimitNotify" />
    <message type="141" name="GcDailyResHelpAmountUpdate" />
    <message type="142" name="CgResHelpInfo" />
    <message type="143" name="GcResHelpInfo" />
    <message type="144" name="GcPlayerModOrGmFlag" />
    <message type="145" name="GcUpdateUsableEmoji" />
    <message type="146" name="CgPlayerFuncSwitchChange" />
    <message type="147" name="GcPlayerFuncSwitchChange" />
    <message type="148" name="GcUnlock" />
    <message type="149" name="CgCheckText" />
    <message type="150" name="GcCheckText" />
    <message type="151" name="CgUnlock" />
    <message type="153" name="CgStoryStage" />
    <message type="154" name="GcStoryStage" />
    <message type="155" name="CgStoryEventUpdate" />
    <message type="156" name="GcStoryEventUpdate" />
    <message type="157" name="CgStoryEventReward" />
    <message type="158" name="GcStoryEventReward" />
    <message type="159" name="CgVipChatControlSwitch" />
    <message type="160" name="GcVipChatControlSwitch" />
    <message type="161" name="CgStoryEventGet" />
    <message type="199" name="GcVerificationFailNotice" />
    <message type="200" name="GcCityInfo" />
    <message type="202" name="GcCityUpdated" />
    <message type="204" name="GcCityRemvoe" />
    <message type="209" name="GcCityOnlineCreate" />
    <message type="210" name="CgCityOnlineCreate" />
    <message type="212" name="CgLandUpgrade" />
    <message type="220" name="CgBuildingCollect" />
    <message type="221" name="GcBuildingCollect" />
    <message type="222" name="CgBuildingCollectBatch" />
    <message type="223" name="GcBuildingCollectBatch" />
    <message type="226" name="GcCityDefenceInfo" />
    <message type="227" name="CgCityDefenceInfo" />
    <message type="228" name="CgFireFighting" />
    <message type="229" name="GcFireFighting" />
    <message type="231" name="CgFixWall" />
    <message type="233" name="CgImmediateResource" />
    <message type="235" name="GcImmediateResource" />
    <message type="300" name="GcMapNodeAppear" />
    <message type="301" name="GcMapArmyAppear" />
    <message type="302" name="GcMapNodeUpdate" />
    <message type="303" name="CgMapPlayerMove" />
    <message type="304" name="GcMapArmyDisappear" />
    <message type="305" name="GcMapRouteDisappear" />
    <message type="306" name="GcMapNodeDisappear" />
    <message type="312" name="GcMapPlayerMove" />
    <message type="313" name="CgMapSearch" />
    <message type="314" name="GcMapSearch" />
    <message type="315" name="CgMapSearchInfo" />
    <message type="316" name="GcMapSearchInfo" />
    <message type="324" name="CgCityDirectMove" />
    <message type="325" name="GcCityDirectMove" />
    <message type="330" name="CgCityRandomMove" />
    <message type="331" name="GcCityRandomMove" />
    <message type="332" name="CgRequestCityInfo" />
    <message type="333" name="CgMinimap" />
    <message type="334" name="GcMinimap" />
    <message type="400" name="CgSoldierList" />
    <message type="401" name="GcSoldierList" />
    <message type="402" name="CgSoldierTrain" />
    <message type="403" name="GcSoldierUpdate" />
    <message type="404" name="CgSoldierDelete" />
    <message type="405" name="GcSoldierDelete" />
    <message type="407" name="CgSoldierCure" />
    <message type="408" name="CgSoldierLevelUp" />
    <message type="409" name="CgSoldierFreeCure" />
    <message type="410" name="CgSoldierSpeedUp" />
    <message type="411" name="GcSoldierSpeedUp" />
    <message type="500" name="GcArmyProgress" />
    <message type="501" name="GcArmyProgressUpdate" />
    <message type="502" name="GcArmyProgressDelete" />
    <message type="503" name="CgArmyReturn" />
    <message type="504" name="CgArmySpeedup" />
    <message type="507" name="GcBattleStart" />
    <message type="509" name="CgChangeRallyHeaderHide" />
    <message type="510" name="CgArmySetout" />
    <message type="513" name="CgArmyRecall" />
    <message type="517" name="GcArmySetout" />
    <message type="518" name="CgGarrisonArmyKickOut" />
    <message type="519" name="CgArmyPreprogrammedUpdate" />
    <message type="520" name="GcArmyPreprogrammedUpdate" />
    <message type="521" name="CgArmyPreprogrammedInfo" />
    <message type="522" name="GcArmyPreprogrammedInfo" />
    <message type="523" name="CgGetMapArmy" />
    <message type="524" name="GcGetMapArmy" />
    <message type="530" name="CgAutoHuntingSet" />
    <message type="531" name="GcAutoHuntingSet" />
    <message type="532" name="GcAutoHuntingStop" />
    <message type="533" name="GcAutoHuntingArmyReturn" />
    <message type="534" name="GcPlunderRecordList" />
    <message type="535" name="GcPlunderRecordAdd" />
    <message type="645" name="GcGetMailReward" />
    <message type="648" name="CgGetMailRewardVersion2" />
    <message type="650" name="CgWorkTrial" />
    <message type="651" name="GcWorkTrial" />
    <message type="653" name="CgWorkDelete" />
    <message type="654" name="GcWorkDelete" />
    <message type="655" name="CgWorkFastFinish" />
    <message type="659" name="CgWorkSpeedup" />
    <message type="660" name="GcWorkFinish" />
    <message type="666" name="CgWorkRewardAdSpeedUp" />
    <message type="667" name="GcWorkSpeedup" />
    <message type="700" name="CgBagInfo" />
    <message type="701" name="GcBagInfo" />
    <message type="702" name="GcItemAdd" />
    <message type="703" name="GcItemUpdate" />
    <message type="704" name="CgItemUse" />
    <message type="706" name="GcOpenBoxReward" />
    <message type="711" name="CgBatchItemUse" />
    <message type="712" name="GcItemBuyAndUse" />
    <message type="713" name="GcItemUse" />
    <message type="714" name="CgPubTreasureBoxPlusInfo" />
    <message type="715" name="GcPubTreasureBoxPlusInfo" />
    <message type="716" name="CgOpenPubTreasureBoxPlus" />
    <message type="717" name="GcOpenPubTreasureBoxPlus" />
    <message type="718" name="CgCommonFragment" />
    <message type="719" name="CgItemBathBuyAndUse" />
    <message type="720" name="GcItemBathBuyAndUse" />
    <message type="721" name="CgItemBuy" />
    <message type="722" name="GcItemBuy" />
    <message type="802" name="CgAllianceCreate" />
    <message type="803" name="GcAllianceCreate" />
    <message type="804" name="CgAllianceRequest" />
    <message type="805" name="GcAllianceRequest" />
    <message type="806" name="CgAllianceRequestCheck" />
    <message type="807" name="GcAllianceRequestCheck" />
    <message type="808" name="GcAllianceRequestCheckNotice" />
    <message type="809" name="CgAllianceExit" />
    <message type="810" name="GcAllianceExit" />
    <message type="811" name="CgAllianceKick" />
    <message type="812" name="GcAllianceKick" />
    <message type="813" name="CgAllianceLoad" />
    <message type="814" name="GcAllianceLoad" />
    <message type="815" name="CgAllianceMemberList" />
    <message type="816" name="GcAllianceMemberList" />
    <message type="817" name="CgAllianceDismiss" />
    <message type="818" name="GcAllianceDismiss" />
    <message type="819" name="CgAllianceNameExist" />
    <message type="820" name="GcAllianceNameExist" />
    <message type="821" name="CgAllianceSearch" />
    <message type="822" name="GcAllianceSearch" />
    <message type="823" name="CgAllianceUpdateAliasName" />
    <message type="824" name="GcAllianceUpdateAliasName" />
    <message type="825" name="CgAllianceUpdateName" />
    <message type="826" name="GcAllianceUpdateName" />
    <message type="827" name="CgAllianceUpdateAutoAgree" />
    <message type="828" name="GcAllianceUpdateAutoAgree" />
    <message type="829" name="CgAllianceUpdateNotice" />
    <message type="830" name="GcAllianceUpdateNotice" />
    <message type="831" name="CgAllianceUpdateDeclaration" />
    <message type="832" name="GcAllianceUpdateDeclaration" />
    <message type="833" name="CgAllianceUpdateLanguage" />
    <message type="834" name="GcAllianceUpdateLanguage" />
    <message type="835" name="CgAllianceUpdateRankName" />
    <message type="836" name="GcAllianceUpdateRankName" />
    <message type="837" name="CgAllianceRankUpdate" />
    <message type="838" name="GcAllianceRankUpdate" />
    <message type="839" name="CgAllianceUpdateLeader" />
    <message type="840" name="GcAllianceUpdateLeader" />
    <message type="841" name="CgAllianceUpdateFlag" />
    <message type="842" name="GcAllianceUpdateFlag" />
    <message type="843" name="CgAllianceSettingUpdate" />
    <message type="844" name="GcAllianceSettingUpdate" />
    <message type="846" name="CgAllianceSetNeedLevel" />
    <message type="847" name="GcAllianceSetNeedLevel" />
    <message type="848" name="CgAllianceSetAutoClear" />
    <message type="849" name="GcAllianceSetAutoClear" />
    <message type="852" name="CgAllianceMemberOnlineAmount" />
    <message type="853" name="GcAllianceMemberOnlineAmount" />
    <message type="854" name="CgAllianceRequestCancel" />
    <message type="855" name="GcAllianceRequestListForRole" />
    <message type="856" name="CgAllianceRequestListForAlliance" />
    <message type="857" name="GcAllianceRequestListForAlliance" />
    <message type="858" name="CgAllianceRequestListForRole" />
    <message type="859" name="GcAllianceRequestAddForRole" />
    <message type="860" name="GcAllianceRequestDeleteForRole" />
    <message type="863" name="GcAllianceApplyAmount" />
    <message type="864" name="CgAllianceShortNameExist" />
    <message type="865" name="GcAllianceShortNameExist" />
    <message type="866" name="GcAllianceProsperityUpdate" />
    <message type="867" name="GcAllianceRankRequestAmount" />
    <message type="870" name="CgAllianceInactiveManagementUpdate" />
    <message type="871" name="GcAllianceInactiveManagementUpdate" />
    <message type="872" name="CgAllianceRankRequestUpdate" />
    <message type="873" name="GcAllianceRankRequestUpdate" />
    <message type="874" name="CgAllianceRankRequest" />
    <message type="875" name="GcAllianceRankRequest" />
    <message type="876" name="CgAllianceRankRequestList" />
    <message type="877" name="GcAllianceRankRequestList" />
    <message type="878" name="CgAllianceRankCheck" />
    <message type="879" name="GcAllianceRankCheckNotice" />
    <message type="880" name="CgAllianceRankAuthSettingUpdate" />
    <message type="881" name="GcAllianceRankAuthSettingUpdate" />
    <message type="882" name="CgAllianceRankAuthSettingInfo" />
    <message type="883" name="GcAllianceMemberUpdate" />
    <message type="884" name="GcAllianceRankAuthSettingInfo" />
    <message type="885" name="CgAllianceSetCamp" />
    <message type="886" name="GcAllianceSetCamp" />
    <message type="897" name="CgAllianceCreateConditionMeet" />
    <message type="898" name="GcAllianceCreateConditionMeet" />
    <message type="900" name="CgAllianceInviteSearch" />
    <message type="901" name="GcAllianceInviteSearch" />
    <message type="904" name="CgAllianceInviteBroadcast" />
    <message type="905" name="CgAllianceInviteOne" />
    <message type="906" name="GcAllianceInviteOne" />
    <message type="907" name="CgAllianceInviteReply" />
    <message type="908" name="GcAllianceInviteReply" />
    <message type="909" name="CgAllianceSetPosition" />
    <message type="910" name="GcAllianceSetPosition" />
    <message type="911" name="GcAllianceLevelPush" />
    <message type="912" name="CgAllianceSetGovernor" />
    <message type="916" name="GcAllianceWarList" />
    <message type="917" name="GcAllianceWarAdd" />
    <message type="918" name="GcAllianceWarDelete" />
    <message type="919" name="GcAllianceWarUpdate" />
    <message type="920" name="GcAllianceWarDefenceUpdate" />
    <message type="922" name="CgAllianceWarKick" />
    <message type="923" name="CgRallyDismiss" />
    <message type="930" name="GcAllianceReinforce" />
    <message type="931" name="CgAllianceReinforceList" />
    <message type="932" name="GcAllianceReinforceList" />
    <message type="933" name="CgAllianceReinforceKick" />
    <message type="934" name="GcAllianceReinforceKick" />
    <message type="935" name="GcAllianceReinforceChange" />
    <message type="943" name="CgReinforceTargetInfo" />
    <message type="944" name="GcReinforceTargetInfo" />
    <message type="945" name="CgAllianceWarSpeedUpOtherArmy" />
    <message type="946" name="CgRallyArmySpeedUp" />
    <message type="947" name="CgFirstJoinAllianceReward" />
    <message type="948" name="GcFirstJoinAllianceReward" />
    <message type="949" name="CgNewAllianceWelcome" />
    <message type="950" name="CgAllianceDailyWelfareStatistical" />
    <message type="951" name="GcAllianceDailyWelfareStatistical" />
    <message type="961" name="CgAllianceOpRankList" />
    <message type="962" name="GcAllianceOpRankList" />
    <message type="971" name="GcFreeTreasureBoxInfo" />
    <message type="972" name="CgFreeTreasureBoxGetReward" />
    <message type="973" name="GcFreeTreasureBoxGetReward" />
    <message type="976" name="GcDailyGiftHeroFragment" />
    <message type="977" name="CgDailyGiftSetHeroFragment" />
    <message type="978" name="GcDailyGiftSetHeroFragment" />
    <message type="991" name="CgAllianceLeaderMissionList" />
    <message type="992" name="GcAllianceLeaderMissionList" />
    <message type="993" name="GcAllianceLeaderMissionRedPoint" />
    <message type="994" name="CgAllianceLeaderMissionReward" />
    <message type="995" name="GcAllianceLeaderMissionReward" />
    <message type="1002" name="CgChatRoomSendMessage" />
    <message type="1003" name="CgGetFriendOnlineState" />
    <message type="1004" name="GcGetFriendOnlineState" />
    <message type="1005" name="CgChatRoomStatistic" />
    <message type="1100" name="CgFavoriteGet" />
    <message type="1101" name="GcFavoriteGet" />
    <message type="1102" name="CgFavoriteAdd" />
    <message type="1103" name="GcFavoriteAdd" />
    <message type="1104" name="CgFavoriteDelete" />
    <message type="1130" name="CgRecordList" />
    <message type="1131" name="GcRecordList" />
    <message type="1160" name="GcNoticeInfo" />
    <message type="1161" name="GcCancelNotice" />
    <message type="1200" name="CgBuffList" />
    <message type="1201" name="GcBuffList" />
    <message type="1202" name="GcBuffUpdate" />
    <message type="1203" name="GcBuffRemove" />
    <message type="1250" name="GcPlayerRechargeNotice" />
    <message type="1251" name="CgPlayerRechargeTest" />
    <message type="1252" name="GcLiBaoList" />
    <message type="1255" name="GcLiBaoBuyRecords" />
    <message type="1256" name="GcPlayerRechargeInfo" />
    <message type="1257" name="CgClaimFirstRechargeRewards" />
    <message type="1258" name="GcClaimFirstRechargeRewards" />
    <message type="1259" name="GcPlayerDoubleRechargeInfo" />
    <message type="1260" name="GcMonthCardInfo" />
    <message type="1261" name="GcMonthCardUpdate" />
    <message type="1262" name="CgMonthCardReward" />
    <message type="1263" name="GcMonthCardReward" />
    <message type="1264" name="CgDailyGiftBoxReward" />
    <message type="1266" name="GcLiBaoUpdate" />
    <message type="1267" name="CgDiamondBuyLiBao" />
    <message type="1268" name="GcExtraBuildingNumsUpdate" />
    <message type="1269" name="CgMonthCardInfo" />
    <message type="1273" name="GcFirstRechargeMark" />
    <message type="1274" name="GcLiBaoListUpdate" />
    <message type="1275" name="GcPurchaseDiamondsSuccess" />
    <message type="1276" name="CgFreeBuyProduct" />
    <message type="1277" name="CgTokenBuyProduct" />
    <message type="1300" name="CgWatchtowerList" />
    <message type="1301" name="GcWatchtowerList" />
    <message type="1302" name="CgWatchtowerIgnore" />
    <message type="1303" name="GcWatchtowerIgnore" />
    <message type="1304" name="CgWatchtowerIgnoreAll" />
    <message type="1305" name="GcWatchtowerIgnoreAll" />
    <message type="1306" name="GcWatchtowerDelete" />
    <message type="1307" name="GcWatchtowerAdd" />
    <message type="1308" name="GcWatchtowerMod" />
    <message type="1309" name="CgWatchtowerArmyProp" />
    <message type="1310" name="GcWatchtowerArmyProp" />
    <message type="1400" name="CgHeadUploadToken" />
    <message type="1401" name="GcHeadUploadToken" />
    <message type="1402" name="CgHeadUploadSuccess" />
    <message type="1403" name="GcHeadUploadSuccess" />
    <message type="1404" name="CgHeadReport" />
    <message type="1405" name="GcHeadReport" />
    <message type="1406" name="CgHeadChange" />
    <message type="1407" name="GcHeadChange" />
    <message type="1408" name="GcAvatarUploadAudit" />
    <message type="1409" name="CgFigureUpdate" />
    <message type="1410" name="GcFigureUpdate" />
    <message type="1460" name="CgAllianceHelpAmount" />
    <message type="1461" name="GcAllianceHelpAmount" />
    <message type="1462" name="CgGetAllianceHelps" />
    <message type="1463" name="GcGetAllianceHelps" />
    <message type="1464" name="CgAllianceHelpProgress" />
    <message type="1465" name="GcAllianceHelpProgress" />
    <message type="1466" name="CgAllianceHelpAllProgress" />
    <message type="1467" name="GcAllianceHelpAllProgress" />
    <message type="1468" name="CgAskAllianceHelp" />
    <message type="1469" name="GcMyAllianceHelpList" />
    <message type="1470" name="GcAllianceHelpPointRefresh" />
    <message type="1501" name="CgAllianceReplaceLeaderRequest" />
    <message type="1502" name="GcAllianceReplaceLeaderRequest" />
    <message type="1503" name="CgAllianceReplaceLeaderCancel" />
    <message type="1504" name="GcAllianceReplaceLeaderCancel" />
    <message type="1505" name="CgAllianceReplaceLeaderReject" />
    <message type="1506" name="GcAllianceReplaceLeaderReject" />
    <message type="1523" name="CgAllianceSuggest" />
    <message type="1524" name="GcAllianceSuggest" />
    <message type="1525" name="CgAllianceOnlineNotice" />
    <message type="1528" name="CgAllianceAutoJoin" />
    <message type="1531" name="GcAllianceGiftRedPoint" />
    <message type="1532" name="CgAllianceGiftInfo" />
    <message type="1533" name="GcAllianceGiftInfo" />
    <message type="1534" name="CgAllianceGiftReward" />
    <message type="1535" name="GcAllianceGiftReward" />
    <message type="1537" name="GcAllianceGiftGetPush" />
    <message type="1538" name="CgSwitchAllianceGiftAnonymity" />
    <message type="1539" name="GcSwitchAllianceGiftAnonymity" />
    <message type="1541" name="GcAllianceMarkPush" />
    <message type="1542" name="CgAllianceMarkSet" />
    <message type="1543" name="GcAllianceMarkSet" />
    <message type="1544" name="CgAllianceMarkDel" />
    <message type="1545" name="GcAllianceMarkDel" />
    <message type="1546" name="GcAllianceMarkDelNotify" />
    <message type="1551" name="CgGetAllianceBenefitsEvent" />
    <message type="1552" name="GcGetAllianceBenefitsEvent" />
    <message type="1553" name="CgAllocateAllianceBenefitsEvent" />
    <message type="1554" name="GcAllocateAllianceBenefitsEvent" />
    <message type="1555" name="CgRegionCapitalAllocation" />
    <message type="1556" name="GcRegionCapitalAllocation" />
    <message type="1557" name="CgRegionCapitalReward" />
    <message type="1558" name="GcRegionCapitalReward" />
    <message type="1751" name="CgRankList" />
    <message type="1752" name="GcRankList" />
    <message type="1753" name="CgNearRankServerState" />
    <message type="1754" name="GcNearRankServerState" />
    <message type="1755" name="CgRankAllianceInfo" />
    <message type="1756" name="GcRankAllianceInfo" />
    <message type="1757" name="CgAllianceLeaderboard" />
    <message type="1758" name="GcAllianceLeaderboard" />
    <message type="1759" name="CgOtherPlayerInfoView" />
    <message type="1760" name="GcOtherPlayerInfoView" />
    <message type="1761" name="CgGetRankFirstInfo" />
    <message type="1762" name="GcGetRankFirstInfo" />
    <message type="1763" name="CgRolePropDetail" />
    <message type="1764" name="GcRolePropDetail" />
    <message type="1767" name="CgLegionRank" />
    <message type="1768" name="GcLegionRank" />
    <message type="1769" name="CgPartPowerRatio" />
    <message type="1770" name="GcPartPowerRation" />
    <message type="1780" name="CgSelfAllianceRank" />
    <message type="1781" name="GcSelfAllianceRank" />
    <message type="1951" name="CgAcitivityList" />
    <message type="1952" name="GcAcitivityList" />
    <message type="1953" name="GcAcitivityUpdate" />
    <message type="1954" name="GcCrownedKingMissionList" />
    <message type="1955" name="GcCrownedKingMissionUpdate" />
    <message type="1956" name="CgReceiveCrownedKingMissionReward" />
    <message type="1957" name="GcReceiveCrownedKingMissionReward" />
    <message type="1958" name="CgReceiveCrownedKingMissionBoxReward" />
    <message type="1959" name="GcReceiveCrownedKingMissionBoxReward" />
    <message type="1960" name="CgActivityInfo" />
    <message type="1961" name="CgActivityReceiveReward" />
    <message type="1969" name="CgActivityExchangeItem" />
    <message type="1970" name="GcActivityExchangeItem" />
    <message type="1971" name="GcActivityExchangeInfo" />
    <message type="1972" name="CgActivityExchangeStore" />
    <message type="1973" name="GcActivityExchangeStore" />
    <message type="1982" name="GcActivityReceiveReward" />
    <message type="1981" name="GcActivityContinuousRechargeInfo" />
    <message type="1991" name="GcActivitySingleRechargeInfo" />
    <message type="1993" name="CgReceiveCrownedKingBox" />
    <message type="1994" name="CgCommonActivityReceiveBox" />
    <message type="1995" name="GcCommonActivityReceiveBox" />
    <message type="2000" name="CgGetNewPlayerGift" />
    <message type="2001" name="GcGetNewPlayerGift" />
    <message type="2002" name="GcNewPlayerGiftStatus" />
    <message type="2003" name="CgNewStrongestLordInfo" />
    <message type="2004" name="GcNewStrongestLordInfo" />
    <message type="2005" name="CgNewStrongestLordRankInfo" />
    <message type="2006" name="GcNewStrongestLordRankInfo" />
    <message type="2007" name="CgNewStrongestLordScoreReward" />
    <message type="2008" name="GcNewStrongestLordScoreReward" />
    <message type="2009" name="CgRemoveActivitySpecRedPoint" />
    <message type="2011" name="CgGetActivityMissionInfo" />
    <message type="2012" name="GcGetActivityMissionInfo" />
    <message type="2013" name="GcActivityMissionUpdate" />
    <message type="2014" name="CgActivityMissionReward" />
    <message type="2015" name="GcActivityMissionReward" />
    <message type="2051" name="CgOnlineReward" />
    <message type="2052" name="GcOnlineReward" />
    <message type="2053" name="CgGetOnlineReward" />
    <message type="2054" name="GcGetOnlineReward" />
    <message type="2107" name="CgSaleItem" />
    <message type="2108" name="GcSaleItem" />
    <message type="2124" name="GcWorldBossRefreshPosition" />
    <message type="2452" name="GcRewardedAdInfo" />
    <message type="2453" name="CgRewardedAdInfo" />
    <message type="2454" name="CgRewardedAdRequest" />
    <message type="2531" name="CgCityWorkQueueList" />
    <message type="2532" name="GcCityWorkQueueList" />
    <message type="2533" name="GcWorkQueueUpdate" />
    <message type="3208" name="CgCommanderInfo" />
    <message type="3209" name="GcCommanderInfo" />
    <message type="3321" name="CgGetCommonActivityMissionInfo" />
    <message type="3322" name="GcGetCommonActivityMissionInfo" />
    <message type="3323" name="CgCommonActivityMissionReward" />
    <message type="3324" name="GcCommonActivityMissionReward" />
    <message type="3325" name="CgWechatActivityMissionProcessUpdate" />
    <message type="3326" name="GcCommonActivityMissionUpdate" />
    <message type="3327" name="CgNewActivityRedPoint" />
    <message type="3328" name="GcNewActivityRedPointPush" />
    <message type="3380" name="CgGetEvaluationReward" />
    <message type="3381" name="GcGetEvaluationReward" />
    <message type="3382" name="GcEvaluationRewardInfo" />
    <message type="3390" name="CgAllianceLeaderSummon" />
    <message type="3391" name="GcAllianceLeaderSummon" />
    <message type="3392" name="CgAllianceLeaderSummonNotify" />
    <message type="3431" name="CgAllianceMessageBord" />
    <message type="3432" name="GcAllianceMessageBord" />
    <message type="3433" name="CgLeaveAllianceMessageBord" />
    <message type="3434" name="CgAllianceMessageBordSend" />
    <message type="3435" name="GcAllianceMessageBordSend" />
    <message type="3436" name="CgAllianceMessageBordStatusSet" />
    <message type="3437" name="GcAllianceMessageBordStatus" />
    <message type="3438" name="CgAllianceMessageBordDelete" />
    <message type="3439" name="GcAllianceMessageBordDelete" />
    <message type="3440" name="GcAllianceMessageBordRedPoints" />
    <message type="3471" name="GcContinuousRechargeInfo" />
    <message type="3475" name="GcContinuousRechargeRewardInfo" />
    <message type="3472" name="CgContinuousRechargeReward" />
    <message type="3473" name="GcContinuousRechargeReward" />
    <message type="3474" name="GcContinuousRechargeData" />
    <message type="3521" name="GcResourceOutputUpdate" />
    <message type="3522" name="GcResourceOutputList" />
    <message type="3580" name="CgMilestoneList" />
    <message type="3581" name="GcMilestoneList" />
    <message type="3582" name="CgMilestoneGetReward" />
    <message type="3583" name="GcMilestoneGetReward" />
    <message type="3584" name="CgGetMilestoneAlliance" />
    <message type="3585" name="GcGetMilestoneAlliance" />
    <message type="3601" name="GcNewAllianceSimpleInfoList" />
    <message type="3602" name="GcUpdateAllianceSimpleInfo" />
    <message type="3605" name="GcDeleteAllianceSimpleInfo" />
    <message type="3701" name="CgGiveUpNewRes" />
    <message type="3702" name="GcGiveUpNewRes" />
    <message type="4001" name="CgGetShieldRecordInfo" />
    <message type="4002" name="GcShieldRecordInfo" />
    <message type="4101" name="GcEquipList" />
    <message type="4102" name="GcUpdateEquipList" />
    <message type="4103" name="CgEquipDress" />
    <message type="4104" name="GcEquipDress" />
    <message type="4113" name="CgEquipUpgrade" />
    <message type="4114" name="GcEquipUpgrade" />
    <message type="4115" name="CgEquipForge" />
    <message type="4116" name="GcEquipForge" />
    <message type="4117" name="CgExclusiveUpgrade" />
    <message type="4118" name="GcExclusiveUpgrade" />
    <message type="4119" name="CgAutoDress" />
    <message type="4120" name="GcAutoDress" />
    <message type="4121" name="CgBestDress" />
    <message type="4122" name="GcBestDress" />
    <message type="4331" name="GcWhispererActivityInfo" />
    <message type="4332" name="CgWhispererDifficultyChallenge" />
    <message type="4333" name="GcWhispererDifficultyChallenge" />
    <message type="4334" name="CgCallWhisperer" />
    <message type="4335" name="GcCallWhisperer" />
    <message type="4336" name="CgWhispererActivityCallHelpList" />
    <message type="4337" name="GcWhispererActivityCallHelpList" />
    <message type="4338" name="CgWhispererActivityAskForHelp" />
    <message type="4339" name="GcWhispererActivityAskForHelp" />
    <message type="4341" name="GcActivityWhispererBossUpdate" />
    <message type="4342" name="GcActivityWhispererRoleRecordUpdate" />
    <message type="4343" name="GcWhispererActivityCallHelpAdd" />
    <message type="4344" name="GcWhispererActivityCallHelpDel" />
    <message type="4345" name="CgWhispererActivityInfo" />
    <message type="4351" name="GcDailyRechargeInfo" />
    <message type="4352" name="CgDailyRechargeMaxReward" />
    <message type="4353" name="GcDailyRechargeMaxReward" />
    <message type="4354" name="CgChooseDailyRechargeMaxReward" />
    <message type="4401" name="CgDeleteAccount" />
    <message type="4402" name="CgDeleteAccountCondition" />
    <message type="4403" name="GcDeleteAccountCondition" />
    <message type="4404" name="GcDeleteAccountErrorCode" />
    <message type="4405" name="CgDeleteGDPRInfo" />
    <message type="4451" name="CgEditAllianceAnnouncement" />
    <message type="4452" name="GcEditAllianceAnnouncement" />
    <message type="4453" name="GcAllianceAnnouncement" />
    <message type="4461" name="CgGetRoleList" />
    <message type="4462" name="GcGetRoleList" />
    <message type="4463" name="CgGetServerList" />
    <message type="4464" name="GcGetServerList" />
    <message type="4465" name="CgCreateNewRole" />
    <message type="4466" name="GcCreateNewRole" />
    <message type="4467" name="CgSwitchRole" />
    <message type="4468" name="GcSwitchRole" />
    <message type="4502" name="CgRecommendRoleList" />
    <message type="4503" name="GcRecommendRoleList" />
    <message type="4504" name="CgGetExcellentMask" />
    <message type="4505" name="GcGetExcellentMask" />
    <message type="4591" name="GcQuestionnaireInfo" />
    <message type="4592" name="CgFinishQuestionnaire" />
    <message type="4593" name="CgReceiveQuestionnaireRewards" />
    <message type="4594" name="GcReceiveQuestionnaireRewards" />
    <message type="4581" name="CgGetTitleList" />
    <message type="4582" name="GcGetTitleList" />
    <message type="4583" name="GcTitleUpdate" />
    <message type="4584" name="CgChangeTitle" />
    <message type="4651" name="CgCommunityReward" />
    <message type="4652" name="GcCommunityRecord" />
    <message type="4653" name="CgCommunityRecord" />
    <message type="4665" name="CgAllianceLogInfo" />
    <message type="4666" name="GcAllianceLogInfo" />
    <message type="4752" name="CgSearchRoleIdByPlatformOriginal" />
    <message type="4753" name="GcSearchRoleIdByPlatformOriginal" />
    <message type="4754" name="CgReplaceAccountDeviceIdByPlatformOriginal" />
    <message type="4755" name="GcReplaceAccountDeviceIdByPlatformOriginal" />
    <message type="4805" name="CgClaimVipDisposableRewards" />
    <message type="4806" name="GcClaimVipDisposableRewards" />
    <message type="4822" name="GcAllianceAffairList" />
    <message type="4823" name="CgAllianceAffairEdit" />
    <message type="4824" name="GcAllianceAffairDelete" />
    <message type="4825" name="CgAllianceAffairSign" />
    <message type="4826" name="GcAllianceAffairUpdate" />
    <message type="4827" name="GcAllianceAffairTop" />
    <message type="4851" name="GcSeasonPush" />
    <message type="5001" name="GcRookieGiftInfo" />
    <message type="5002" name="CgReceiveRookieGiftFreeReward" />
    <message type="5003" name="GcReceiveRookieGiftReward" />
    <message type="5004" name="CgClickRookieGift" />
    <message type="5031" name="CgPlayerWorldExploreRecord" />
    <message type="5032" name="GcPlayerWorldExploreRecord" />
    <message type="5033" name="CgPlayerWorldExploreEvent" />
    <message type="5034" name="GcPlayerWorldExploreEvent" />
    <message type="5035" name="GcAddPlayerWorldExploreEvent" />
    <message type="5036" name="GcDeletePlayerWorldExploreEvent" />
    <message type="5037" name="GcUpdatePlayerWorldExploreEvent" />
    <message type="5038" name="GcPlayerWorldExploreEventUp" />
    <message type="5039" name="GcPlayerWorldExploreRewardUp" />
    <message type="5040" name="CgPlayerWorldExploreEventReciveReward" />
    <message type="5041" name="GcPlayerWorldExploreEventReciveReward" />
    <message type="5042" name="CgExploreEventPveSceneBattleResult" />
    <message type="5043" name="GcPlayerShowWorldExploreEventNpcLevel" />
    <message type="5044" name="CgExploreEventRewardUp" />
    <message type="5045" name="GcExploreEventRewardUp" />
    <message type="5046" name="CgPlayerWorldExploreEventReciveAllReward" />
    <message type="5047" name="GcPlayerWorldExploreEventReciveAllReward" />
    <message type="5091" name="CgGetDropGroupItemWeight" />
    <message type="5092" name="GcGetDropGroupItemWeight" />
    <message type="5171" name="GcShootingTrainingInfo" />
    <message type="5172" name="CgShootingTrainingSelect" />
    <message type="5173" name="GcShootingTrainingSelect" />
    <message type="5174" name="CgShootingTrainingRefresh" />
    <message type="5175" name="GcShootingTrainingRefresh" />
    <message type="5176" name="CgShootingTrainingPay" />
    <message type="5177" name="GcShootingTrainingPay" />
    <message type="5178" name="CgGetBulletForFree" />
    <message type="5179" name="GcGetBulletForFree" />
    <message type="5212" name="CgStrongestLordsRankFirstInfo" />
    <message type="5211" name="GcStrongestLordsRankFirstInfo" />
    <message type="5213" name="CgRemoveRedPoint" />
    <message type="5220" name="GcFreeBuyInfo" />
    <message type="5221" name="CgFreeBuyPay" />
    <message type="5222" name="GcFreeBuyPay" />
    <message type="5223" name="CgFreeBuyReward" />
    <message type="5224" name="GcFreeBuyReward" />
    <message type="5225" name="CgFreeBuyPayGet" />
    <message type="5226" name="GcFreeBuyPayGet" />
    <message type="5521" name="CgCsaHonorHallInfo" />
    <message type="5522" name="GcCsaHonorHallInfo" />
    <message type="5631" name="CgIntegratedSpringboard" />
    <message type="5632" name="GcIntegratedSpringboard" />
    <message type="5633" name="CgSpringBoardReward" />
    <message type="5671" name="GcAchieveInfo" />
    <message type="5672" name="CgReceiveAchieveTaskReward" />
    <message type="5673" name="GcReceiveAchieveTaskReward" />
    <message type="5674" name="CgReceiveAchievePointReward" />
    <message type="5675" name="GcReceiveAchievePointReward" />
    <message type="5676" name="CgSelectAchieveIdList" />
    <message type="5677" name="GcSelectAchieveIdList" />
    <message type="5678" name="CgLookOtherPlayerAchieveInfo" />
    <message type="5679" name="GcLookOtherPlayerAchieveInfo" />
    <message type="5680" name="GcAchieveUpdateInfo" />
    <message type="5681" name="CgCancelAchieveIdList" />
    <message type="5682" name="GcCancelAchieveIdList" />
    <message type="5921" name="CgSelectWhispererAllianceBoss" />
    <message type="5922" name="GcSelectWhispererAllianceBoss" />
    <message type="5925" name="CgWhispererAllianceBossReportList" />
    <message type="5927" name="CgWhispererAllianceBossSetCallTime" />
    <message type="5928" name="GcWhispererAllianceBossSetCallTime" />
    <message type="5929" name="CgWhispererAllianceBossBoxReward" />
    <message type="5930" name="GcWhispererAllianceBossBoxReward" />
    <message type="5961" name="CgBingoActivityInfo" />
    <message type="5962" name="GcBingoActivityInfo" />
    <message type="5963" name="CgBingoOperateCell" />
    <message type="5964" name="GcBingoOperateCell" />
    <message type="5965" name="CgBingoRefreshCell" />
    <message type="5966" name="GcBingoRefreshCell" />
    <message type="5967" name="CgBingoGetReward" />
    <message type="5968" name="GcBingoGetReward" />
    <message type="5969" name="CgBingoSeasonRewardInfo" />
    <message type="5970" name="GcBingoSeasonRewardInfo" />
    <message type="5971" name="CgBingoGetDailyBox" />
    <message type="5972" name="GcBingoGetDailyBox" />
    <message type="5984" name="CgLoginRewardInfo" />
    <message type="5985" name="GcLoginRewardInfo" />
    <message type="5986" name="CgReceiveLoginReward" />
    <message type="5987" name="GcReceiveLoginReward" />
    <message type="6011" name="CgWxCollectionReward" />
    <message type="6012" name="GcWxCollectionReward" />
    <message type="6013" name="CgWxCollectionFinish" />
    <message type="6014" name="GcWxCollectionFinish" />
    <message type="6201" name="GcDiamondCountConsumerInfo" />
    <message type="6202" name="CgDiamondCountConsumerReward" />
    <message type="6203" name="GcDiamondCountConsumerReward" />
    <message type="6211" name="GcAllianceGatheringPlaceInfo" />
    <message type="6212" name="CgAllianceGatheringPlaceSet" />
    <message type="6213" name="GcAllianceGatheringPlaceSet" />
    <message type="6214" name="CgAllianceGatheringPlaceGoGoGo" />
    <message type="6215" name="GcAllianceGatheringPlaceGoGoGo" />
    <message type="6408" name="CgArmyBuildMapRoute" />
    <message type="6409" name="GcArmyBuildMapRoute" />
    <message type="7201" name="CgBattleLineupInfo" />
    <message type="7202" name="GcBattleLineupInfo" />
    <message type="7203" name="CgBattleLineupUpdate" />
    <message type="7204" name="GcBattleLineupUpdate" />
    <message type="8001" name="CgIcePullWheelRequest" />
    <message type="8002" name="GcIcePullWheelResponse" />
    <message type="8003" name="CgIceWheelBoxReceive" />
    <message type="8004" name="GcIceWheelBoxReceive" />
    <message type="8010" name="GcIceWheelData" />
    <message type="8005" name="CgIceWheelFreeGift" />
    <message type="8006" name="GcIceWheelFreeGift" />
    <message type="8007" name="CgIceWheelBuyItem" />
    <message type="8008" name="GcIceWheelBuyItem" />
    <message type="8009" name="CgIceWheelChooseHero" />
    <message type="8101" name="CgBrotherHoodReward" />
    <message type="8102" name="GcBrotherHoodReward" />
    <message type="8100" name="GcBrotherHoodInfo" />
    <message type="8110" name="CgTokenEventActivityInfo" />
    <message type="8111" name="GcTokenEventActivityInfo" />
    <message type="8121" name="CgInviteCodeInfo" />
    <message type="8122" name="GcInviteCodeInfo" />
    <message type="8123" name="CgModifyInviteCode" />
    <message type="8124" name="GcModifyInviteCode" />
    <message type="8125" name="CgBindInviteCode" />
    <message type="8126" name="GcBindInviteCode" />
    <message type="8127" name="CgReceiveInviteCodeReward" />
    <message type="8128" name="GcReceiveInviteCodeReward" />
    <message type="8200" name="GcSelectionPackInfo" />
    <message type="8201" name="CgSelectionPackSelect" />
    <message type="8211" name="GcProtectedByAttackInfo" />
    <message type="8212" name="CgProtectedByAttackToast" />
    <message type="8213" name="GcBattleLoseCompensationInfo" />
    <message type="8214" name="CgReceiveBattleLoseCompensation" />
    <message type="8215" name="GcReceiveBattleLoseCompensation" />
    <message type="8221" name="GcSignInEventInfo" />
    <message type="8222" name="CgSignInEventReward" />
    <message type="8223" name="GcSignInEventReward" />
    <message type="8241" name="CgLoyaltyHallInfo" />
    <message type="8242" name="GcLoyaltyHallInfo" />
    <message type="8243" name="CgLoyaltyHallReward" />
    <message type="8261" name="GcSnowEventInfo" />
    <message type="8262" name="CgSnowEventDonateItem" />
    <message type="8263" name="GcSnowEventDonateItem" />
    <message type="8264" name="CgSnowEventBoxReward" />
    <message type="8265" name="GcSnowEventBoxReward" />
    <message type="1600" name="CgPushRegisterToken" />
    <message type="1601" name="GcPushRegisterToken" />
    <message type="1610" name="GcPushSettingInfo" />
    <message type="1611" name="CgPushSettingMod" />
    <message type="1612" name="GcPushSettingMod" />
    <message type="1620" name="CgWechatSubInfo" />
    <message type="1621" name="GcWechatSubInfo" />
    <message type="1622" name="CgWechatSubInfoSync" />
    <message type="1623" name="GcWechatSubInfoSync" />
    <message type="1624" name="CgWechatSubscribeReward" />
    <message type="1625" name="GcWechatSubscribeReward" />
    <message type="1626" name="CgWechatSubCountAdd" />
    <message type="1627" name="GcWechatSubCountSync" />
    <message type="7371" name="CgActivityPlayerRankInfo" />
    <message type="7372" name="GcActivityPlayerRankInfo" />
    <message type="7373" name="CgActivityAllianceRankInfo" />
    <message type="7374" name="GcActivityAllianceRankInfo" />
    <message type="7375" name="CgActivityGetMissionReward" />
    <message type="7376" name="GcActivityGetMissionReward" />
    <message type="7377" name="CgActivityPlayerRankHistoryList" />
    <message type="7378" name="GcActivityPlayerRankHistoryList" />
    <message type="7379" name="CgActivityAllianceRankHistoryList" />
    <message type="7380" name="GcActivityAllianceRankHistoryList" />
    <message type="7381" name="CgActivityPlayerRankHistoryInfo" />
    <message type="7382" name="GcActivityPlayerRankHistoryInfo" />
    <message type="7383" name="CgActivityAllianceRankHistoryInfo" />
    <message type="7384" name="GcActivityAllianceRankHistoryInfo" />
    <message type="7385" name="CgScoreActivityMissionInfo" />
    <message type="7386" name="GcScoreActivityMissionInfo" />
    <message type="7387" name="CgActivityCalendar" />
    <message type="7388" name="GcActivityCalendar" />
    <message type="7681" name="CgLuckyBagActivityInfo" />
    <message type="7682" name="GcLuckyBagActivityInfo" />
    <message type="7683" name="GcLuckyBagUpdate" />
    <message type="7684" name="CgLuckyBagStart" />
    <message type="7685" name="CgLuckyBagChoose" />
    <message type="7686" name="GcRedPackList" />
    <message type="7687" name="CgRedPackOpen" />
    <message type="7688" name="GcRedPackOpen" />
    <message type="7689" name="GcRedPackUpdate" />
    <message type="7690" name="GcRedPackNew" />
    <message type="7691" name="GcRedPackPlayerInfo" />
    <message type="7541" name="CgReceiveHorse" />
    <message type="7542" name="GcReceiveHorse" />
    <message type="7543" name="CgHorseFavorUp" />
    <message type="7544" name="GcHorseFavorUp" />
    <message type="7545" name="GcHorseCultivate" />
    <message type="7401" name="CgNanmanDetailInfo" />
    <message type="7402" name="GcNanmanDetailInfo" />
    <message type="7403" name="CgNanmanHelpInfo" />
    <message type="7404" name="GcNanmanHelpInfo" />
    <message type="7405" name="CgNanmanSchedule" />
    <message type="7406" name="GcNanmanSchedule" />
    <message type="7407" name="CgNanmanMissionInfo" />
    <message type="7408" name="GcNanmanMissionInfo" />
    <message type="7241" name="GcActivityNotice" />
    <message type="7242" name="CgVassalShow" />
    <message type="7243" name="GcVassalShow" />
    <message type="1340" name="CgAlipayCheckSetHome" />
    <message type="1341" name="GcAlipayCheckSetHome" />
    <message type="7231" name="CgGetAllianceBattleInfo" />
    <message type="7232" name="GcGetAllianceBattleInfo" />
    <message type="7233" name="CgOpenAllianceBattle" />
    <message type="7234" name="GcOpenAllianceBattle" />
    <message type="7235" name="CgGetAllianceBattleReward" />
    <message type="7236" name="GcGetAllianceBattleReward" />
    <message type="7237" name="GcGetAllianceBattleScorePush" />
    <message type="7238" name="GcReceivedScorePush" />
    <message type="7121" name="CgAllianceBossBattleInfo" />
    <message type="7122" name="GcAllianceBossBattleInfo" />
    <message type="7123" name="CgDonateBuild" />
    <message type="7124" name="CgBattleDonate" />
    <message type="7125" name="GcBattleDonate" />
    <message type="7126" name="CgBuildDrillGround" />
    <message type="7127" name="CgDismantleDrillGround" />
    <message type="7128" name="CgStartBattleAllianceBoss" />
    <message type="7129" name="CgDonateBuildBoard" />
    <message type="7130" name="CgCanPlacePoint" />
    <message type="7131" name="GcCanPlacePoint" />
    <message type="7132" name="GcOpenPush" />
    <message type="7133" name="GcStartedOpenPush" />
    <message type="7134" name="CgGetAllianceTotalDamage" />
    <message type="7135" name="GcGetAllianceTotalDamage" />
    <message type="2360" name="CgAllianceTechList" />
    <message type="2361" name="GcAllianceTechList" />
    <message type="2362" name="CgAllianceTechUpgrade" />
    <message type="2363" name="GcAllianceTechUpgrade" />
    <message type="2364" name="CgAllianceTechDonate" />
    <message type="2365" name="GcAllianceTechDonate" />
    <message type="2366" name="CgAllianceTechRecommend" />
    <message type="2367" name="GcAllianceTechRecommend" />
    <message type="2368" name="GcAllianceTechChange" />
    <message type="2369" name="GcAllianceTechUpgradeFinish" />
    <message type="2370" name="GcAllianceTechRedPoint" />
    <message type="5151" name="CgAllianceBuildingTechList" />
    <message type="5152" name="GcAllianceBuildingTechList" />
    <message type="5153" name="CgAllianceBuildingTechUpgrade" />
    <message type="5154" name="GcAllianceBuildingTechUpgrade" />
    <message type="5155" name="CgAllianceBuildingInfo" />
    <message type="5156" name="GcAllianceBuildingInfo" />
    <message type="5157" name="CgAllianceBuildingSet" />
    <message type="5158" name="GcAllianceBuildingSet" />
    <message type="5159" name="CgAllianceBuildingUpgrade" />
    <message type="5160" name="GcAllianceBuildingUpgrade" />
    <message type="5161" name="CgAllianceBuildingDemolition" />
    <message type="5162" name="GcAllianceBuildingDemolition" />
    <message type="5163" name="CgAllianceBuildingMove" />
    <message type="5164" name="GcAllianceBuildingMove" />
    <message type="5165" name="CgAllianceBuildingCityMove" />
    <message type="5166" name="GcAllianceBuildingCityMove" />
    <message type="5167" name="CgAllianceBuildingSetOrder" />
    <message type="5168" name="CgAllianceBuildingAuto" />
    <message type="5169" name="CgAllianceBuildingRecall" />
    <message type="5170" name="CgAllianceBuildingReturn" />
    <message type="7801" name="CgPlaceFeast" />
    <message type="7802" name="CgMoveFeast" />
    <message type="7803" name="CgOpenAllianceFeast" />
    <message type="7804" name="GcFeastHallInfo" />
    <message type="7805" name="GcFeastOpenPush" />
    <message type="7806" name="GcFeastStartPush" />
    <message type="7807" name="CgAllianceFeastInfo" />
    <message type="7808" name="GcAllianceFeastInfo" />
    <message type="7809" name="CgAllianceBuildingCanPlace" />
    <message type="7810" name="GcAllianceBuildingCanPlace" />
    <message type="7811" name="CgAllianceBuyFeastRecord" />
    <message type="7812" name="GcAllianceBuyFeastRecord" />
    <message type="7813" name="CgMyAttendFeastRecords" />
    <message type="7814" name="GcMyAttendFeastRecords" />
    <message type="7815" name="CgPlaceFeastHall" />
    <message type="7816" name="CgRemoveFeastHall" />
    <message type="7817" name="CgNearbyFeast" />
    <message type="7818" name="GcNearbyFeast" />
    <message type="7819" name="CgFeastNodeInfo" />
    <message type="7820" name="GcFeastNodeInfo" />
    <message type="5251" name="CgAllianceMissionList" />
    <message type="5252" name="GcAllianceMissionList" />
    <message type="5253" name="GcAllianceMissionRedPoint" />
    <message type="5254" name="CgAllianceMissionReward" />
    <message type="5255" name="GcAllianceMissionReward" />
    <message type="7751" name="CgSetAllianceRecruitSwitch" />
    <message type="7752" name="GcAllianceRecruitSwitchStatus" />
    <message type="7753" name="CgAllianceRecruitInfo" />
    <message type="7754" name="GcAllianceRecruitInfo" />
    <message type="7755" name="GcAllianceRecruitUpdate" />
    <message type="7756" name="CgAllianceRecruitOffer" />
    <message type="7757" name="GcAllianceRecruitOffer" />
    <message type="6800" name="CgArenaChallengerInfo" />
    <message type="6801" name="GcArenaChallengerInfo" />
    <message type="6802" name="CgArenaChallengerList" />
    <message type="6803" name="GcArenaChallengerList" />
    <message type="6804" name="CgArenaChallenge" />
    <message type="6805" name="GcArenaChallenge" />
    <message type="6806" name="CgArenaBuyTicket" />
    <message type="6807" name="GcArenaBuyTicket" />
    <message type="6808" name="CgArenaSetDefenceLineup" />
    <message type="6809" name="GcArenaSetDefenceLineup" />
    <message type="6810" name="CgArenaRecords" />
    <message type="6811" name="GcArenaRecords" />
    <message type="6812" name="CgArenaPlayerInfo" />
    <message type="6813" name="GcArenaPlayerInfo" />
    <message type="6814" name="CgArenaRankTop" />
    <message type="6815" name="GcArenaRankTop" />
    <message type="6816" name="CgArenaRecordRedPoint" />
    <message type="6817" name="GcArenaRecordRedPoint" />
    <message type="6850" name="GcAutoRallyInfo" />
    <message type="6851" name="CgAutoRallyInfo" />
    <message type="6852" name="CgModifyAutoRallySetting" />
    <message type="6853" name="CgAutoRallyDetail" />
    <message type="6854" name="GcAutoRallyDetail" />
    <message type="6855" name="CgOfflineRallyInfo" />
    <message type="6760" name="GcBlizzardResistInfo" />
    <message type="6765" name="GcBlizzardInfo" />
    <message type="6766" name="CgBlizzardGetReward" />
    <message type="6767" name="GcBlizzardGetReward" />
    <message type="6768" name="CgBlizzardStart" />
    <message type="6769" name="GcBlizzardStart" />
    <message type="7841" name="CgBuildSearchProgress" />
    <message type="7842" name="GcBuildSearchProgress" />
    <message type="7843" name="CgBuildSearch" />
    <message type="7844" name="GcBuildSearchResult" />
    <message type="7002" name="GcCaravanOpenTime" />
    <message type="7003" name="CgCaravanMyselfList" />
    <message type="7004" name="GcCaravanMyselfList" />
    <message type="7005" name="CgCaravanCreate" />
    <message type="7006" name="GcCaravanCreate" />
    <message type="7007" name="CgCaravanRefreshQuality" />
    <message type="7008" name="GcCaravanRefreshQuality" />
    <message type="7009" name="CgCaravanStart" />
    <message type="7010" name="GcCaravanStart" />
    <message type="7011" name="CgCaravanReward" />
    <message type="7012" name="GcCaravanReward" />
    <message type="7021" name="CgCaravanPlunderList" />
    <message type="7022" name="GcCaravanPlunderList" />
    <message type="7023" name="CgCaravanPlunderInfo" />
    <message type="7024" name="GcCaravanPlunderInfo" />
    <message type="7025" name="CgCaravanPlunder" />
    <message type="7026" name="GcCaravanPlunder" />
    <message type="7027" name="CgCaravanPlunderRecord" />
    <message type="7028" name="GcCaravanPlunderRecord" />
    <message type="7029" name="CgCaravanTradeRecord" />
    <message type="7030" name="GcCaravanTradeRecord" />
    <message type="7031" name="CgCaravanTradeRecordDetail" />
    <message type="7032" name="GcCaravanTradeRecordDetail" />
    <message type="7033" name="CgCaravanTradeArmy" />
    <message type="7034" name="GcCaravanTradeArmy" />
    <message type="7035" name="CgCaravanBePlunderStatus" />
    <message type="7036" name="GcCaravanBePlunderStatus" />
    <message type="5101" name="CgEnterCSAServer" />
    <message type="5102" name="CgExitCSAServer" />
    <message type="5103" name="CgAllianceLoadCSA" />
    <message type="5104" name="GcAllianceLoadCSA" />
    <message type="5105" name="CgAllianceMemberListCSA" />
    <message type="5106" name="GcAllianceMemberListCSA" />
    <message type="5107" name="GcAllianceMemberUpdateCSA" />
    <message type="5108" name="CgCSAAllianceMemberPoints" />
    <message type="5109" name="GcCSAAllianceMemberPoints" />
    <message type="5110" name="GcCSAMigrateErrorCode" />
    <message type="5111" name="CgCSATrophyList" />
    <message type="5112" name="GcCSATrophyList" />
    <message type="5113" name="CgCSATrophyBuild" />
    <message type="5114" name="GcCSATrophyBuild" />
    <message type="5115" name="CgCSATrophyRetract" />
    <message type="5116" name="GcCSATrophyRetract" />
    <message type="5120" name="GcCSAStageInfo" />
    <message type="5121" name="CgCSAGetKingInfo" />
    <message type="5122" name="GcCSAGetKingInfo" />
    <message type="5123" name="CgCSASelectBattleTime" />
    <message type="5124" name="GcCSASelectBattleTime" />
    <message type="5130" name="GcCsaRoleBattleInfo" />
    <message type="5131" name="GcCsaBattleScoreInfo" />
    <message type="5132" name="CgCsaWinTimeRewardList" />
    <message type="5133" name="GcCsaWinTimeRewardList" />
    <message type="5134" name="GcCsaCsaWinTimeRewardUpdate" />
    <message type="5135" name="CgGetCsaCsaWinTimeReward" />
    <message type="5136" name="GcGetCsaCsaWinTimeReward" />
    <message type="5137" name="GcBuffUpdateInfo" />
    <message type="5138" name="CgCSAServerRankList" />
    <message type="5139" name="GcCSAServerRankList" />
    <message type="5140" name="CgCsaMissionList" />
    <message type="5141" name="GcCsaMissionList" />
    <message type="5142" name="GcCsaMissionUpdate" />
    <message type="5143" name="CgCsaMissionGetReward" />
    <message type="5144" name="GcCsaMissionGetReward" />
    <message type="5145" name="CgCSAMinMapWorldCastle" />
    <message type="5146" name="GcCSAMinMapWorldCastle" />
    <message type="5147" name="GcCSAWorldCastleChangeOwner" />
    <message type="5148" name="GcCSAActivityInfoV2" />
    <message type="7300" name="GcEnlistmentInfo" />
    <message type="7301" name="CgEnlist" />
    <message type="7302" name="GcEnlist" />
    <message type="4613" name="GcExpeditionReward" />
    <message type="4614" name="CgExpeditionBtlVerify" />
    <message type="4615" name="GcExpeditionBtlResult" />
    <message type="4616" name="CgExpeditionInfo" />
    <message type="4617" name="GcExpeditionInfo" />
    <message type="4618" name="CgExpeditionHangup" />
    <message type="4619" name="GcExpeditionHangup" />
    <message type="4620" name="GcHangupPush" />
    <message type="4622" name="CgExpeditionGroupReward" />
    <message type="4623" name="GcExpeditionGroupReward" />
    <message type="4624" name="CgExpeditionGroupNext" />
    <message type="4625" name="GcExpeditionGroupNext" />
    <message type="4626" name="CgExpeditionInnerReward" />
    <message type="4627" name="GcExpeditionInnerReward" />
    <message type="4628" name="CgExpeditionInnerInfo" />
    <message type="4629" name="GcExpeditionInnerInfo" />
    <message type="4671" name="GcExpeditionMulReward" />
    <message type="4672" name="CgExpeditionMulBtlVerify" />
    <message type="4673" name="GcExpeditionMulBtlResult" />
    <message type="4674" name="CgExpeditionMulInfo" />
    <message type="4675" name="GcExpeditionMulInfo" />
    <message type="4681" name="CgExpeditionMulGroupReward" />
    <message type="4682" name="GcExpeditionMulGroupReward" />
    <message type="4683" name="CgExpeditionMulGroupNext" />
    <message type="4684" name="GcExpeditionMulGroupNext" />
    <message type="2201" name="CgGetFashionList" />
    <message type="2202" name="GcGetFashionList" />
    <message type="2205" name="CgFashionLockRequest" />
    <message type="2207" name="CgFashionActiveRequest" />
    <message type="2208" name="GcFashionUnlockNotify" />
    <message type="2209" name="GcWorldEmojiAdd" />
    <message type="2210" name="CgUseWorldEmoji" />
    <message type="2211" name="GcUseWorldEmoji" />
    <message type="2212" name="GcWorldEmoji" />
    <message type="2213" name="CgSettingWorldEmoji" />
    <message type="7821" name="GcFissionOverview" />
    <message type="7822" name="GcFissionUpdate" />
    <message type="7823" name="CgExchangeFissionProp" />
    <message type="7824" name="CgDrawFissionReward" />
    <message type="7825" name="GcDrawFissionReward" />
    <message type="7826" name="CgBuyFissionUniversalItem" />
    <message type="7827" name="GcExchangeFissionProp" />
    <message type="3908" name="GcGVGStageInfo" />
    <message type="3909" name="CgGVGAllianceQualified" />
    <message type="3910" name="GcGVGAllianceQualified" />
    <message type="3911" name="GcGVGSignInfo" />
    <message type="3912" name="CgGVGSelectTimeList" />
    <message type="3913" name="GcGVGSelectTimeList" />
    <message type="3914" name="CgGVGSelectTime" />
    <message type="3915" name="CgGVGSelectMember" />
    <message type="3916" name="GcGVGError" />
    <message type="3917" name="CgGVGEnter" />
    <message type="3918" name="CgGVGExit" />
    <message type="3919" name="CgGVGBuyStamina" />
    <message type="3920" name="CgGVGBuyMoveCity" />
    <message type="3921" name="GcGVGBattleInfo" />
    <message type="3922" name="CgStrongHoldDetailInfo" />
    <message type="3923" name="GcStrongHoldDetailInfo" />
    <message type="3924" name="GcGvgBattleServerInfo" />
    <message type="3925" name="GcGvgBattleServerTimeInfo" />
    <message type="3926" name="CgGVGSignInfo" />
    <message type="3927" name="CgAllianceLoadGVG" />
    <message type="3928" name="GcAllianceLoadGVG" />
    <message type="3929" name="CgAllianceMemberListGVG" />
    <message type="3930" name="GcAllianceMemberListGVG" />
    <message type="3931" name="GcAllianceMemberUpdateGVG" />
    <message type="3932" name="CgGVGResultInfo" />
    <message type="3933" name="GcGVGResultInfo" />
    <message type="3934" name="GcStrongHoldRefreshNpc" />
    <message type="3935" name="GcStrongHoldFirstInfo" />
    <message type="3936" name="GcStrongHoldResultInfo" />
    <message type="3937" name="CgGVGAllianceMemberInfo" />
    <message type="3938" name="GcGVGAllianceMemberInfo" />
    <message type="3939" name="GcGVGBuildingChangeOwnerNotice" />
    <message type="3940" name="GcGVGEnterNum" />
    <message type="3941" name="CgLastGVGRankList" />
    <message type="3942" name="GcLastGVGRankList" />
    <message type="3943" name="GcGVGRankMemberInfo" />
    <message type="3944" name="CgGvgCupApply" />
    <message type="3945" name="CgGvgCupStageInfo" />
    <message type="3946" name="GcGvgCupStageInfo" />
    <message type="3947" name="CgGVGRankMemberInfo" />
    <message type="3948" name="CgGvgTournamentChampion" />
    <message type="3949" name="GcGvgTournamentChampion" />
    <message type="3950" name="CgCureWoundedSoldier" />
    <message type="3951" name="GcCureWoundedSoldier" />
    <message type="3952" name="CgGvgObserveSettlement" />
    <message type="3953" name="GcGvgObserveSettlement" />
    <message type="3954" name="CgGvgObserveMatchList" />
    <message type="3955" name="GcGvgObserveMatchList" />
    <message type="3956" name="CgGvgObserveEnter" />
    <message type="3957" name="GcGvgObserveEnter" />
    <message type="3958" name="CgObserveAllReport" />
    <message type="3959" name="GcObserveAllReport" />
    <message type="3960" name="CgObserveStrongNodeReport" />
    <message type="3961" name="GcObserveStrongNodeReport" />
    <message type="3962" name="CgEngageCreateRoomList" />
    <message type="3963" name="GcEngageCreateRoomList" />
    <message type="3964" name="CgEngageCreateTimeList" />
    <message type="3965" name="GcEngageCreateTimeList" />
    <message type="3966" name="CgEngageCreateRoom" />
    <message type="3967" name="GcEngageCreateRoom" />
    <message type="3968" name="CgEngageModifyRoom" />
    <message type="3969" name="GcEngageModifyRoom" />
    <message type="3970" name="CgEngageApplyList" />
    <message type="3971" name="GcEngageApplyList" />
    <message type="3972" name="CgEngageJoinRoom" />
    <message type="3973" name="GcEngageJoinRoom" />
    <message type="3974" name="CgEngageInvite" />
    <message type="3975" name="GcEngageInvite" />
    <message type="3976" name="CgEngageCheck" />
    <message type="3977" name="GcEngageCheck" />
    <message type="3978" name="CgEngageSelectMember" />
    <message type="3979" name="GcEngageSelectMember" />
    <message type="3980" name="CgEngageMatchRoom" />
    <message type="3981" name="GcEngageMatchRoom" />
    <message type="3982" name="GcRZEStageInfo" />
    <message type="3988" name="CgGvgRegister" />
    <message type="3989" name="CgGvgExpandInfo" />
    <message type="3990" name="GcGvgExpandInfo" />
    <message type="3991" name="CgGvgStrongHoldStationSetOrder" />
    <message type="3992" name="GcGvgStrongHoldStationSetOrder" />
    <message type="9101" name="CgGvgRoleWillChange" />
    <message type="9102" name="GcGvgRoleWillStatus" />
    <message type="9103" name="CgGvgMemberList" />
    <message type="9104" name="GcGvgMemberList" />
    <message type="9105" name="CgGvgChangeLineUp" />
    <message type="9106" name="GcGvgChangeLineUp" />
    <message type="9107" name="GcGVGFinalResultShow" />
    <message type="9108" name="CgGVGHistoryInfo" />
    <message type="9109" name="GcGVGHistoryInfo" />
    <message type="9110" name="GcGvGWuChaoSupplyWagonBegin" />
    <message type="9111" name="GcGvGWuChaoSupplyWagonEnd" />
    <message type="9112" name="CgGvGWuChaoInfoReq" />
    <message type="9113" name="GcGvGStrongHoldMarchSimpleInfo" />
    <message type="9114" name="GcPiliCheAttack" />
    <message type="9115" name="GcGVGBuildingOpen" />
    <message type="9116" name="CgGvGPlayerCountInfo" />
    <message type="9117" name="GcGvGPlayerCountInfo" />
    <message type="9118" name="CgGvGStrongHoldAutoChooseLeader" />
    <message type="9119" name="GcGVGCureSoldierSkill" />
    <message type="9120" name="GcGVGBattlleLogin" />
    <message type="1651" name="GcHeroList" />
    <message type="1652" name="GcHeroAdd" />
    <message type="1653" name="GcHeroMod" />
    <message type="1654" name="GcHeroDel" />
    <message type="1657" name="CgHeroGet" />
    <message type="1658" name="GcHeroGet" />
    <message type="1659" name="CgHeroRankUpgrade" />
    <message type="1660" name="GcHeroRankUpgrade" />
    <message type="1661" name="CgHeroSkillLevelUp" />
    <message type="1662" name="GcHeroSkillLevelUp" />
    <message type="1665" name="CgHeroExchange" />
    <message type="1666" name="GcHeroExchange" />
    <message type="1671" name="GcNoticeGetHero" />
    <message type="1672" name="CgHeroSkillActivate" />
    <message type="1673" name="GcHeroSkillActivate" />
    <message type="1676" name="CgHeroSkillUse" />
    <message type="1677" name="GcHeroSkillUse" />
    <message type="1678" name="CgHeroReset" />
    <message type="1679" name="CgHeroResetTime" />
    <message type="1680" name="GcHeroResetTime" />
    <message type="1681" name="CgOtherHeroList" />
    <message type="1682" name="GcOtherHeroList" />
    <message type="1683" name="GcHeroReset" />
    <message type="1684" name="CgHeroCompound" />
    <message type="1685" name="GcHeroCompound" />
    <message type="1686" name="CgHeroLevelUp" />
    <message type="1687" name="GcHeroLevelUp" />
    <message type="1688" name="CgHeroReceiveAward" />
    <message type="1689" name="GcHeroReceiveAward" />
    <message type="1690" name="CgSetDefendHero" />
    <message type="1691" name="GcDefendHeroUpdate" />
    <message type="1700" name="GcHeroSkillSpeedProduction" />
    <message type="1702" name="GcHeroSkillSoldierAdd" />
    <message type="7461" name="GcHorseList" />
    <message type="7462" name="GcHorseUpdate" />
    <message type="7463" name="CgHorseExchange" />
    <message type="7464" name="GcHorseExchange" />
    <message type="7465" name="CgHorseLevelUp" />
    <message type="7466" name="GcHorseLevelUp" />
    <message type="7467" name="CgHorseStarUp" />
    <message type="7468" name="GcHorseStarUp" />
    <message type="7469" name="CgHorseEquipLevelUp" />
    <message type="7470" name="GcHorseEquipLevelUp" />
    <message type="7471" name="CgHorseMateList" />
    <message type="7472" name="GcHorseMateList" />
    <message type="7473" name="CgHorseMate" />
    <message type="7474" name="GcHorseMate" />
    <message type="7475" name="CgHorseMateRecord" />
    <message type="7476" name="GcHorseMateRecord" />
    <message type="7477" name="CgHorseMateGetReward" />
    <message type="7478" name="GcHorseMateGetReward" />
    <message type="7479" name="CgHorseMateCancel" />
    <message type="7480" name="GcHorseMateCancel" />
    <message type="7481" name="CgHorseMateJoin" />
    <message type="7482" name="GcHorseMateJoin" />
    <message type="7483" name="CgHorseMateSelf" />
    <message type="7484" name="GcHorseMateSelf" />
    <message type="7485" name="CgHorseMateShare" />
    <message type="7486" name="GcHorseMateShare" />
    <message type="7487" name="CgHorseRename" />
    <message type="7488" name="GcHorseRename" />
    <message type="4901" name="CgKVKEnter" />
    <message type="4902" name="CgKVKExit" />
    <message type="4903" name="GcKVKStageInfo" />
    <message type="4904" name="CgKvkMatchGroupInfo" />
    <message type="4905" name="GcKvkMatchGroupInfo" />
    <message type="4906" name="GcKvkSafeRegion" />
    <message type="4946" name="CgGetSeasonWeekReward" />
    <message type="4947" name="GcGetSeasonWeekReward" />
    <message type="4948" name="GcSeasonWeekTakenPush" />
    <message type="4949" name="GcHonorWeekValueUpdate" />
    <message type="4950" name="CgSeasonTaskList" />
    <message type="4951" name="GcSeasonTaskList" />
    <message type="4952" name="CgGetSeasonTaskReward" />
    <message type="4953" name="GcGetSeasonTaskReward" />
    <message type="4954" name="CgGetSeasonPersonalReward" />
    <message type="4955" name="GcGetSeasonPersonalReward" />
    <message type="4956" name="GcSeasonPersonalRewardedList" />
    <message type="4957" name="GcHonorValueUpdate" />
    <message type="4958" name="CgAllocAllianceReward" />
    <message type="4959" name="GcAllocAllianceReward" />
    <message type="4960" name="CgGetKingInfo" />
    <message type="4961" name="GcGetKingInfo" />
    <message type="4962" name="CgLikeKing" />
    <message type="4963" name="GcKVKMove" />
    <message type="5701" name="CgLegionCreate" />
    <message type="5702" name="GcLegionCreate" />
    <message type="5703" name="CgLegionMemberList" />
    <message type="5704" name="GcLegionMemberList" />
    <message type="5705" name="CgLegionList" />
    <message type="5706" name="GcLegionList" />
    <message type="5707" name="CgLegionRequest" />
    <message type="5708" name="GcLegionRequest" />
    <message type="5709" name="CgLegionRequestCheck" />
    <message type="5710" name="GcLegionRequestCheck" />
    <message type="5711" name="GcLegionRequestCheckNotice" />
    <message type="5712" name="CgLegionExit" />
    <message type="5713" name="GcLegionExit" />
    <message type="5714" name="CgLegionKick" />
    <message type="5715" name="GcLegionKick" />
    <message type="5716" name="CgLegionLoad" />
    <message type="5717" name="GcLegionLoad" />
    <message type="5718" name="GcLegionRequestCount" />
    <message type="5719" name="CgLegionRequestListForLegion" />
    <message type="5720" name="GcLegionRequestListForLegion" />
    <message type="5721" name="CgLegionDismiss" />
    <message type="5722" name="GcLegionDismiss" />
    <message type="5723" name="CgLegionTransfer" />
    <message type="5724" name="GcLegionTransfer" />
    <message type="5725" name="CgLegionUpdate" />
    <message type="5726" name="CgLegionOfficialList" />
    <message type="5727" name="GcLegionOfficialList" />
    <message type="5728" name="CgLegionOfficialUpdate" />
    <message type="5729" name="CgLegionSearch" />
    <message type="5730" name="GcLegionSearch" />
    <message type="5731" name="CgLegionNameExist" />
    <message type="5732" name="GcLegionNameExist" />
    <message type="5733" name="CgLegionAllianceMemberList" />
    <message type="5734" name="GcLegionAllianceMemberList" />
    <message type="5735" name="CgLegionRequestCancel" />
    <message type="5736" name="GcLegionLogList" />
    <message type="5737" name="GcLegionLogAdd" />
    <message type="5738" name="GcLegionMarkList" />
    <message type="5739" name="CgLegionMarkSet" />
    <message type="5740" name="GcLegionMarkSet" />
    <message type="5741" name="GcLegionMarkSetNotify" />
    <message type="5742" name="CgLegionMarkDel" />
    <message type="5743" name="GcLegionMarkDel" />
    <message type="5744" name="GcLegionMarkDelNotify" />
    <message type="5745" name="CgEditLegionAnnouncement" />
    <message type="5746" name="GcEditLegionAnnouncement" />
    <message type="5747" name="GcLegionAnnouncement" />
    <message type="5750" name="CgAllocateLegionBenefitsEvent" />
    <message type="5751" name="GcAllocateLegionBenefitsEvent" />
    <message type="7351" name="GcLordTreasureInfo" />
    <message type="7352" name="CgLordTreasureForge" />
    <message type="7353" name="GcLordTreasureForge" />
    <message type="7361" name="CgSetLordTreasureInfoHide" />
    <message type="7362" name="GcSetLordTreasureInfoHide" />
    <message type="3861" name="CgMigrateServerList" />
    <message type="3862" name="GcMigrateServerList" />
    <message type="3863" name="CgMigrateCondition" />
    <message type="3864" name="GcMigrateCondition" />
    <message type="3865" name="CgMigrateServer" />
    <message type="3867" name="GcMigrateErrorCode" />
    <message type="3868" name="CgMigrateServerListForSeason" />
    <message type="3869" name="CgMigrateServerForSeason" />
    <message type="1350" name="CgMissionList" />
    <message type="1351" name="GcMissionList" />
    <message type="1352" name="CgMissionReward" />
    <message type="1353" name="GcMissionReward" />
    <message type="1354" name="GcMissionUpdate" />
    <message type="1355" name="GcDailyMissionList" />
    <message type="1356" name="GcDailyMissionUpdate" />
    <message type="1357" name="CgDailyMissionReward" />
    <message type="1358" name="GcDailyMissionReward" />
    <message type="1359" name="CgDailyLivenessReward" />
    <message type="1360" name="GcDailyLivenessReward" />
    <message type="1380" name="CgChapterMissionRewardAll" />
    <message type="1381" name="GcChapterMissionRewardAll" />
    <message type="1382" name="CgChapterMissionReward" />
    <message type="1383" name="GcChapterMissionReward" />
    <message type="1384" name="GcChapterMissionUpdate" />
    <message type="1393" name="GcChapterMissionInfo" />
    <message type="1394" name="CgPopularWillMissionReward" />
    <message type="1395" name="GcPopularWillMissionReward" />
    <message type="1396" name="GcPopularWillMissionUpdate" />
    <message type="1397" name="CgSummonMissionBoss" />
    <message type="1398" name="GcSummonMissionBoss" />
    <message type="6500" name="CgSendMessageToTsServer" />
    <message type="6501" name="GcTsServerSendMessage" />
    <message type="4760" name="GcKingAppointNotification" />
    <message type="4761" name="GcOfficialsPush" />
    <message type="4762" name="CgOfficialAppoint" />
    <message type="4763" name="GcOfficialAppoint" />
    <message type="4764" name="CgOfficialSearchUser" />
    <message type="4765" name="GcOfficialSearchUser" />
    <message type="4766" name="CgOfficialRewardsInfo" />
    <message type="4767" name="GcOfficialRewardsInfo" />
    <message type="4768" name="CgOfficialGrantRewards" />
    <message type="4769" name="GcOfficialGrantRewards" />
    <message type="4770" name="CgOfficialSkillsInfo" />
    <message type="4771" name="GcOfficialSkillsInfo" />
    <message type="4772" name="CgOfficialUseSkill" />
    <message type="4773" name="GcOfficialUseSkill" />
    <message type="4774" name="CgOfficialHistory" />
    <message type="4775" name="GcOfficialHistory" />
    <message type="4776" name="CgOfficialApply" />
    <message type="4777" name="GcOfficialApply" />
    <message type="4778" name="CgOfficialApplyInfo" />
    <message type="4779" name="GcOfficialApplyInfo" />
    <message type="4780" name="CgOfficialAppointmentInfo" />
    <message type="4781" name="GcOfficialAppointmentInfo" />
    <message type="4782" name="CgOfficialMail" />
    <message type="4783" name="GcOfficialMail" />
    <message type="4784" name="CgOfficialInfo" />
    <message type="4785" name="GcOfficialInfo" />
    <message type="4786" name="GcOfficialRedPointNotice" />
    <message type="4787" name="GcOfficialAppointNotice" />
    <message type="4788" name="CgOfficialAppointNotice" />
    <message type="4789" name="GcOfficialTips" />
    <message type="3331" name="GcBattlePassInfo" />
    <message type="3332" name="GcBattlePassUpdateInfo" />
    <message type="3333" name="CgBattlePassBuyLevel" />
    <message type="3335" name="CgBattlePassLevelReward" />
    <message type="3336" name="GcBattlePassLevelReward" />
    <message type="7251" name="CgPeopleInfo" />
    <message type="7252" name="GcPeopleInfo" />
    <message type="7253" name="CgPeopleGotoWork" />
    <message type="7254" name="GcPeopleGotoWork" />
    <message type="7255" name="GcPeopleChangeNotify" />
    <message type="7256" name="CgPeopleRecruit" />
    <message type="7257" name="GcPeopleRecruit" />
    <message type="7258" name="CgPeopleDie" />
    <message type="7259" name="GceopleDie" />
    <message type="7260" name="CgPeopleGotoHospital" />
    <message type="7261" name="GcPeopleGotoHospital" />
    <message type="7262" name="CgPeopleUpdateState" />
    <message type="7263" name="GcPeopleUpdateState" />
    <message type="7264" name="CgPeopleGetFriendPointsReward" />
    <message type="7265" name="GcPeopleGetFriendPointsReward" />
    <message type="7266" name="CgPeopleDismissal" />
    <message type="7267" name="GcPeopleDismissal" />
    <message type="7268" name="CgPeopleReplace" />
    <message type="7269" name="GcPeopleReplace" />
    <message type="7270" name="CgPeopleDieEventInfo" />
    <message type="7271" name="GcPeopleDieEventInfo" />
    <message type="7272" name="CgPeopleDieEventReward" />
    <message type="7273" name="GcPeopleDieEventReward" />
    <message type="7274" name="CgSyncExtraPeopleInfoToFriend" />
    <message type="7275" name="CgPeopleWordStats" />
    <message type="7276" name="GcPeopleWordStats" />
    <message type="7278" name="CgPeopleWordReward" />
    <message type="7279" name="GcPeopleWordReward" />
    <message type="7280" name="CgLastRoleInAccount" />
    <message type="7281" name="GcLastRoleInAccount" />
    <message type="7282" name="CgReportPlayPeople" />
    <message type="7283" name="GcReportPlayPeople" />
    <message type="7284" name="CgReissuePeopleForInteraction" />
    <message type="7101" name="CgPopularWillInfo" />
    <message type="7102" name="GcPopularWillInfo" />
    <message type="7103" name="CgPopularWillSetWorkerState" />
    <message type="7104" name="GcPopularWillSetWorkerState" />
    <message type="7105" name="CgPopularWillUnlockState" />
    <message type="7106" name="GcPopularWillUnlockState" />
    <message type="7107" name="CgPopularWillPeopleChange" />
    <message type="7108" name="GcPopularWillPeopleChange" />
    <message type="7109" name="GcPopularWillChange" />
    <message type="7110" name="GcPopularWillChangeTarget" />
    <message type="7111" name="CgPopularWillSkillInfo" />
    <message type="7112" name="GcPopularWillSkillInfo" />
    <message type="7113" name="CgPopularWillSkillUse" />
    <message type="7114" name="GcPopularWillSkillUse" />
    <message type="7115" name="CgPopularWillProtestSwitch" />
    <message type="7116" name="GcPopularWillProtestSwitch" />
    <message type="7117" name="CgPopularWillComplaintGuide" />
    <message type="7118" name="GcPopularWillComplaintGuide" />
    <message type="6901" name="CgRegionCapitalApply" />
    <message type="6902" name="GcRegionCapitalApply" />
    <message type="6903" name="CgRegionCapitalCancel" />
    <message type="6904" name="GcRegionCapitalCancel" />
    <message type="6905" name="CgRegionCapitalApplyList" />
    <message type="6906" name="GcRegionCapitalApplyList" />
    <message type="6907" name="CgRegionCapitalWarPlan" />
    <message type="6908" name="GcRegionCapitalWarPlan" />
    <message type="6909" name="CgRegionCapitalSimpleList" />
    <message type="6910" name="GcUpdateRegionCapitalSimpleInfo" />
    <message type="6911" name="CgRegionCapitalDetailInfo" />
    <message type="6912" name="GcRegionCapitalDetailInfo" />
    <message type="6913" name="CgRegionCapitalGiveup" />
    <message type="6914" name="GcRegionCapitalGiveup" />
    <message type="6915" name="CgRegionCapitalStationSetOrder" />
    <message type="6917" name="CgRegionCapitalStationAuto" />
    <message type="6918" name="GcRegionCapitalHotConflict" />
    <message type="6930" name="CgRegionCapitalBelongHistory" />
    <message type="6931" name="GcRegionCapitalBelongHistory" />
    <message type="6932" name="CgRegionCapitalArtilleryHistory" />
    <message type="6933" name="GcRegionCapitalArtilleryHistory" />
    <message type="6940" name="CgRegionCapitalRecall" />
    <message type="6942" name="CgRegionCapitalReturn" />
    <message type="6954" name="GcRegionCapitalBelongNotice" />
    <message type="6958" name="GcRegionCapitalStageInfo" />
    <message type="6961" name="CgRoleRegionCapitalTips" />
    <message type="6962" name="GcRoleRegionCapitalTips" />
    <message type="6963" name="CgRegionBattleRecordList" />
    <message type="6964" name="GcRegionBattleRecordList" />
    <message type="6965" name="CgRegionBattleRecord" />
    <message type="6966" name="GcRegionBattleRecord" />
    <message type="6967" name="GcRegionBattleRecordNotify" />
    <message type="6968" name="CgRegionBattleRecordRank" />
    <message type="6969" name="GcRegionBattleRecordRank" />
    <message type="6970" name="CgRoyalMissionInfo" />
    <message type="6971" name="GcRoyalMissionInfo" />
    <message type="6990" name="GcRegionConfig" />
    <message type="6991" name="CgRegionCapitalDanmuSwitch" />
    <message type="6992" name="GcRegionCapitalDanmuNotify" />
    <message type="6771" name="GcRobberInfo" />
    <message type="6772" name="CgRobberGetReward" />
    <message type="6773" name="GcRobberGetReward" />
    <message type="7421" name="CgGetSeasonWarmUpInfo" />
    <message type="7422" name="GcGetSeasonWarmUpInfo" />
    <message type="7423" name="GcSeasonWarmUpPersonalScoreUpdate" />
    <message type="7424" name="GcSeasonWarmUpDynastyScoreUpdate" />
    <message type="7425" name="CgSeasonWarmUpGetDynastyRankInfo" />
    <message type="7426" name="GcSeasonWarmUpGetDynastyRankInfo" />
    <message type="7427" name="CgGetSeasonWarmUpRankInfo" />
    <message type="7428" name="GcGetSeasonWarmUpRankInfo" />
    <message type="7430" name="CgGetSeasonWarmUpReward" />
    <message type="7431" name="GcGetSeasonWarmUpReward" />
    <message type="7432" name="GcSeasonWarmUpRewardRecordPush" />
    <message type="7440" name="GcSeasonWarmUpGroupInfo" />
    <message type="7441" name="CgSeasonWarmUpSetOrder" />
    <message type="7442" name="GcSeasonWarmUpSetOrder" />
    <message type="7443" name="CgSeasonWarmUpSetSafeRegion" />
    <message type="7444" name="GcSeasonWarmUpSetSafeRegion" />
    <message type="7445" name="GcSeasonWarmUpGroupInfoUpdate" />
    <message type="7450" name="GcSeasonWarmUpStageUpdate" />
    <message type="7211" name="CgSummonSevenCapture" />
    <message type="7212" name="GcSummonSevenCapture" />
    <message type="7213" name="CgRedeemSevenCaptureItems" />
    <message type="7214" name="GcRedeemSevenCaptureItems" />
    <message type="7215" name="CgGetTodayJoinRallyTimes" />
    <message type="7216" name="GcGetTodayJoinRallyTimes" />
    <message type="7217" name="GcSummonBossPush" />
    <message type="7321" name="CgShareToOthers" />
    <message type="7322" name="GcShareToOthers" />
    <message type="7323" name="CgShareInfo" />
    <message type="7324" name="GcShareInfo" />
    <message type="7325" name="CgClickShare" />
    <message type="7326" name="CgClickShareNew" />
    <message type="7330" name="GcShareMissionList" />
    <message type="7331" name="GcShareMissionUpdate" />
    <message type="7332" name="CgReceiveShareMissionReward" />
    <message type="7333" name="GcReceiveShareMissionReward" />
    <message type="7334" name="CgShareVoteByMail" />
    <message type="7335" name="GcShareVoteByMail" />
    <message type="7051" name="CgSiegeEnginesBuild" />
    <message type="7052" name="GcSiegeEnginesBuild" />
    <message type="7053" name="CgSiegeEnginesList" />
    <message type="7054" name="GcSiegeEnginesList" />
    <message type="7055" name="CgSiegeEnginesCancelBuild" />
    <message type="7056" name="GcSiegeEnginesCancelBuild" />
    <message type="7057" name="CgAllianceCatapultStationAuto" />
    <message type="7058" name="GcSiegeEnginesWarList" />
    <message type="7059" name="GcSiegeEnginesWarAdd" />
    <message type="7060" name="GcSiegeEnginesWarDelete" />
    <message type="7061" name="GcSiegeEnginesWarUpdate" />
    <message type="7062" name="GcSiegeEnginesNotify" />
    <message type="7063" name="CgSiegeEnginesInfo" />
    <message type="7064" name="GcSiegeEnginesInfo" />
    <message type="7065" name="CgSiegeEnginesReturn" />
    <message type="7066" name="CgSiegeEnginesRecall" />
    <message type="7067" name="CgSiegeEnginesStationSetOrder" />
    <message type="7068" name="CgSiegeBatteringRamStart" />
    <message type="7069" name="GcSiegeBatteringRamStart" />
    <message type="6754" name="CgSimSetHeatingState" />
    <message type="6755" name="GcSimSetHeatingState" />
    <message type="6756" name="CgSimUpgradeLine" />
    <message type="6757" name="GcSimUpgradeLine" />
    <message type="6861" name="CgGetResourceUpdate" />
    <message type="6700" name="CgStoreList" />
    <message type="6701" name="GcStoreList" />
    <message type="6702" name="CgStoreInfo" />
    <message type="6703" name="GcStoreInfo" />
    <message type="6704" name="CgStoreRefresh" />
    <message type="6705" name="GcStoreRefresh" />
    <message type="6706" name="CgStoreBuy" />
    <message type="6707" name="GcStoreBuy" />
    <message type="6710" name="GcStoreUpdate" />
    <message type="6711" name="GcStoreCommodityUpdate" />
    <message type="6721" name="CgFakeBuy" />
    <message type="6722" name="GcFakeBuy" />
    <message type="6725" name="GcTradePostItemList" />
    <message type="6726" name="GcTradePostItemUpdateList" />
    <message type="6727" name="CgBuyTradePostItem" />
    <message type="6728" name="GcBuyTradePostItem" />
    <message type="1801" name="CgTechList" />
    <message type="1802" name="GcTechList" />
    <message type="1803" name="CgTechUpgrade" />
    <message type="1804" name="GcTechUpgrade" />
    <message type="9001" name="CgTestCurrencyInfo" />
    <message type="9002" name="GcTestCurrencyInfo" />
    <message type="9003" name="CgImmediateFinishWork" />
    <message type="9004" name="CgRecoverDurability" />
    <message type="9005" name="CgTestPropValues" />
    <message type="9006" name="GcTestPropValues" />
    <message type="7551" name="CgTrusteeshipInfo" />
    <message type="7552" name="GcTrusteeshipInfo" />
    <message type="7553" name="CgTrusteeshipRequest" />
    <message type="7555" name="CgTrusteeshipCancel" />
    <message type="7557" name="CgTrusteeshipReply" />
    <message type="7559" name="CgTrusteeshipModify" />
    <message type="7565" name="CgTrusteeshipDetailInfo" />
    <message type="7566" name="GcTrusteeshipDetailInfo" />
    <message type="7152" name="GcTVTSettlementInfo" />
    <message type="7153" name="CgTvtBattleRecord" />
    <message type="7154" name="GcTvtBattleRecord" />
    <message type="7155" name="CgGetTvtSeasonSettlementReward" />
    <message type="7156" name="GcGetTvtSeasonSettlementReward" />
    <message type="7157" name="CgTVTActivityExtraInfo" />
    <message type="7158" name="GcTVTActivityExtraInfo" />
    <message type="7160" name="GcTvtStageInfo" />
    <message type="7161" name="CgTvtSignupInfo" />
    <message type="7162" name="GcTvtSignupInfo" />
    <message type="7163" name="CgTvtSignup" />
    <message type="7164" name="GcTvtSignup" />
    <message type="7165" name="CgTvtEnter" />
    <message type="7166" name="GcTvtEnter" />
    <message type="7221" name="CgWorldBossDongzhuoInfo" />
    <message type="7222" name="GcWorldBossDongzhuoInfo" />
    <message type="7223" name="CgActivityWorldBossDongzhuoInfo" />
    <message type="7224" name="GcActivityWorldBossDongzhuoInfo" />
    <message type="7226" name="GcDongzhuoNewRecord" />
    <message type="7451" name="GcDongzhuoDropNewBox" />
    <message type="7452" name="CgDongzhuoBoxReceived" />
    <message type="7453" name="GcDongzhuoBoxReceived" />
    <message type="7454" name="CgDongzhuoBoxInvolved" />
    <message type="7455" name="GcDongzhuoBoxInvolved" />
    <message type="7456" name="CgDongzhuoBoxInfo" />
    <message type="7457" name="GcDongzhuoBoxInfo" />
    <message type="7458" name="CgDongzhuoTreasureBoxInfo" />
    <message type="7459" name="GcDongzhuoTreasureBoxInfo" />
    <message type="7460" name="GcDongzhuoBoxShareInfo" />
    <message type="3616" name="GcWorldCastleDetailInfo" />
    <message type="3617" name="CgGetMapNodePanelInfo" />
    <message type="3618" name="GcWorldCastlePanelInfo" />
    <message type="3619" name="CgCancelMapNodePanelInfo" />
    <message type="3620" name="GcStrongHoldPanelInfo" />
    <message type="7651" name="CgVassalOverview" />
    <message type="7652" name="GcVassalOverview" />
    <message type="7662" name="CgVassalAllianceOverview" />
    <message type="7663" name="GcVassalAllianceOverview" />
</messages>
