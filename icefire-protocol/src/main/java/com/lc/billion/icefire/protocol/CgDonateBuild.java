/**
 * Autogenerated by <PERSON>hr<PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 捐献建造
 * @Message(7123)
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class CgDonateBuild implements org.apache.thrift.TBase<CgDonateBuild, CgDonateBuild._Fields>, java.io.Serializable, Cloneable, Comparable<CgDonateBuild> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("CgDonateBuild");

  private static final org.apache.thrift.protocol.TField ALLIANCE_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("allianceId", org.apache.thrift.protocol.TType.I64, (short)1);
  private static final org.apache.thrift.protocol.TField CURRENCY_TYPE_FIELD_DESC = new org.apache.thrift.protocol.TField("currencyType", org.apache.thrift.protocol.TType.I32, (short)2);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new CgDonateBuildStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new CgDonateBuildTupleSchemeFactory();

  /**
   * 联盟id 无联盟为0
   */
  public long allianceId; // optional
  /**
   * 捐献类型
   */
  public int currencyType; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * 联盟id 无联盟为0
     */
    ALLIANCE_ID((short)1, "allianceId"),
    /**
     * 捐献类型
     */
    CURRENCY_TYPE((short)2, "currencyType");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // ALLIANCE_ID
          return ALLIANCE_ID;
        case 2: // CURRENCY_TYPE
          return CURRENCY_TYPE;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __ALLIANCEID_ISSET_ID = 0;
  private static final int __CURRENCYTYPE_ISSET_ID = 1;
  private byte __isset_bitfield = 0;
  private static final _Fields optionals[] = {_Fields.ALLIANCE_ID,_Fields.CURRENCY_TYPE};
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.ALLIANCE_ID, new org.apache.thrift.meta_data.FieldMetaData("allianceId", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I64)));
    tmpMap.put(_Fields.CURRENCY_TYPE, new org.apache.thrift.meta_data.FieldMetaData("currencyType", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(CgDonateBuild.class, metaDataMap);
  }

  public CgDonateBuild() {
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public CgDonateBuild(CgDonateBuild other) {
    __isset_bitfield = other.__isset_bitfield;
    this.allianceId = other.allianceId;
    this.currencyType = other.currencyType;
  }

  public CgDonateBuild deepCopy() {
    return new CgDonateBuild(this);
  }

  @Override
  public void clear() {
    setAllianceIdIsSet(false);
    this.allianceId = 0;
    setCurrencyTypeIsSet(false);
    this.currencyType = 0;
  }

  /**
   * 联盟id 无联盟为0
   */
  public long getAllianceId() {
    return this.allianceId;
  }

  /**
   * 联盟id 无联盟为0
   */
  public CgDonateBuild setAllianceId(long allianceId) {
    this.allianceId = allianceId;
    setAllianceIdIsSet(true);
    return this;
  }

  public void unsetAllianceId() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __ALLIANCEID_ISSET_ID);
  }

  /** Returns true if field allianceId is set (has been assigned a value) and false otherwise */
  public boolean isSetAllianceId() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __ALLIANCEID_ISSET_ID);
  }

  public void setAllianceIdIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __ALLIANCEID_ISSET_ID, value);
  }

  /**
   * 捐献类型
   */
  public int getCurrencyType() {
    return this.currencyType;
  }

  /**
   * 捐献类型
   */
  public CgDonateBuild setCurrencyType(int currencyType) {
    this.currencyType = currencyType;
    setCurrencyTypeIsSet(true);
    return this;
  }

  public void unsetCurrencyType() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __CURRENCYTYPE_ISSET_ID);
  }

  /** Returns true if field currencyType is set (has been assigned a value) and false otherwise */
  public boolean isSetCurrencyType() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __CURRENCYTYPE_ISSET_ID);
  }

  public void setCurrencyTypeIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __CURRENCYTYPE_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case ALLIANCE_ID:
      if (value == null) {
        unsetAllianceId();
      } else {
        setAllianceId((java.lang.Long)value);
      }
      break;

    case CURRENCY_TYPE:
      if (value == null) {
        unsetCurrencyType();
      } else {
        setCurrencyType((java.lang.Integer)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case ALLIANCE_ID:
      return getAllianceId();

    case CURRENCY_TYPE:
      return getCurrencyType();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case ALLIANCE_ID:
      return isSetAllianceId();
    case CURRENCY_TYPE:
      return isSetCurrencyType();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof CgDonateBuild)
      return this.equals((CgDonateBuild)that);
    return false;
  }

  public boolean equals(CgDonateBuild that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_allianceId = true && this.isSetAllianceId();
    boolean that_present_allianceId = true && that.isSetAllianceId();
    if (this_present_allianceId || that_present_allianceId) {
      if (!(this_present_allianceId && that_present_allianceId))
        return false;
      if (this.allianceId != that.allianceId)
        return false;
    }

    boolean this_present_currencyType = true && this.isSetCurrencyType();
    boolean that_present_currencyType = true && that.isSetCurrencyType();
    if (this_present_currencyType || that_present_currencyType) {
      if (!(this_present_currencyType && that_present_currencyType))
        return false;
      if (this.currencyType != that.currencyType)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + ((isSetAllianceId()) ? 131071 : 524287);
    if (isSetAllianceId())
      hashCode = hashCode * 8191 + org.apache.thrift.TBaseHelper.hashCode(allianceId);

    hashCode = hashCode * 8191 + ((isSetCurrencyType()) ? 131071 : 524287);
    if (isSetCurrencyType())
      hashCode = hashCode * 8191 + currencyType;

    return hashCode;
  }

  @Override
  public int compareTo(CgDonateBuild other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetAllianceId(), other.isSetAllianceId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetAllianceId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.allianceId, other.allianceId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetCurrencyType(), other.isSetCurrencyType());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetCurrencyType()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.currencyType, other.currencyType);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("CgDonateBuild(");
    boolean first = true;

    if (isSetAllianceId()) {
      sb.append("allianceId:");
      sb.append(this.allianceId);
      first = false;
    }
    if (isSetCurrencyType()) {
      if (!first) sb.append(", ");
      sb.append("currencyType:");
      sb.append(this.currencyType);
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class CgDonateBuildStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public CgDonateBuildStandardScheme getScheme() {
      return new CgDonateBuildStandardScheme();
    }
  }

  private static class CgDonateBuildStandardScheme extends org.apache.thrift.scheme.StandardScheme<CgDonateBuild> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, CgDonateBuild struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // ALLIANCE_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I64) {
              struct.allianceId = iprot.readI64();
              struct.setAllianceIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // CURRENCY_TYPE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.currencyType = iprot.readI32();
              struct.setCurrencyTypeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, CgDonateBuild struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.isSetAllianceId()) {
        oprot.writeFieldBegin(ALLIANCE_ID_FIELD_DESC);
        oprot.writeI64(struct.allianceId);
        oprot.writeFieldEnd();
      }
      if (struct.isSetCurrencyType()) {
        oprot.writeFieldBegin(CURRENCY_TYPE_FIELD_DESC);
        oprot.writeI32(struct.currencyType);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class CgDonateBuildTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public CgDonateBuildTupleScheme getScheme() {
      return new CgDonateBuildTupleScheme();
    }
  }

  private static class CgDonateBuildTupleScheme extends org.apache.thrift.scheme.TupleScheme<CgDonateBuild> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, CgDonateBuild struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet optionals = new java.util.BitSet();
      if (struct.isSetAllianceId()) {
        optionals.set(0);
      }
      if (struct.isSetCurrencyType()) {
        optionals.set(1);
      }
      oprot.writeBitSet(optionals, 2);
      if (struct.isSetAllianceId()) {
        oprot.writeI64(struct.allianceId);
      }
      if (struct.isSetCurrencyType()) {
        oprot.writeI32(struct.currencyType);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, CgDonateBuild struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet incoming = iprot.readBitSet(2);
      if (incoming.get(0)) {
        struct.allianceId = iprot.readI64();
        struct.setAllianceIdIsSet(true);
      }
      if (incoming.get(1)) {
        struct.currencyType = iprot.readI32();
        struct.setCurrencyTypeIsSet(true);
      }
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

