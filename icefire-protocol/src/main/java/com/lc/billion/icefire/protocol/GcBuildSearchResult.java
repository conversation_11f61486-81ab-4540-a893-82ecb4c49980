/**
 * Autogenerated by <PERSON><PERSON><PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 建筑内探索结果
 * @Message(7844)
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class GcBuildSearchResult implements org.apache.thrift.TBase<GcBuildSearchResult, GcBuildSearchResult._Fields>, java.io.Serializable, Cloneable, Comparable<GcBuildSearchResult> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("GcBuildSearchResult");

  private static final org.apache.thrift.protocol.TField BUILD_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("buildId", org.apache.thrift.protocol.TType.I32, (short)1);
  private static final org.apache.thrift.protocol.TField POINT_ID_FIELD_DESC = new org.apache.thrift.protocol.TField("pointId", org.apache.thrift.protocol.TType.I32, (short)2);
  private static final org.apache.thrift.protocol.TField ERROR_CODE_FIELD_DESC = new org.apache.thrift.protocol.TField("errorCode", org.apache.thrift.protocol.TType.I32, (short)3);
  private static final org.apache.thrift.protocol.TField REWARDS_FIELD_DESC = new org.apache.thrift.protocol.TField("rewards", org.apache.thrift.protocol.TType.LIST, (short)4);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new GcBuildSearchResultStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new GcBuildSearchResultTupleSchemeFactory();

  /**
   * 建筑ID
   */
  public int buildId; // required
  /**
   * 探索点ID
   */
  public int pointId; // required
  /**
   * 探索结果
   * 
   * @see com.lc.billion.icefire.protocol.constant.PsBuildSearchErrorCode
   */
  public @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.constant.PsBuildSearchErrorCode errorCode; // required
  /**
   * 探索奖励
   */
  public @org.apache.thrift.annotation.Nullable java.util.List<com.lc.billion.icefire.protocol.structure.PsSimpleItem> rewards; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * 建筑ID
     */
    BUILD_ID((short)1, "buildId"),
    /**
     * 探索点ID
     */
    POINT_ID((short)2, "pointId"),
    /**
     * 探索结果
     * 
     * @see com.lc.billion.icefire.protocol.constant.PsBuildSearchErrorCode
     */
    ERROR_CODE((short)3, "errorCode"),
    /**
     * 探索奖励
     */
    REWARDS((short)4, "rewards");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // BUILD_ID
          return BUILD_ID;
        case 2: // POINT_ID
          return POINT_ID;
        case 3: // ERROR_CODE
          return ERROR_CODE;
        case 4: // REWARDS
          return REWARDS;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __BUILDID_ISSET_ID = 0;
  private static final int __POINTID_ISSET_ID = 1;
  private byte __isset_bitfield = 0;
  private static final _Fields optionals[] = {_Fields.REWARDS};
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.BUILD_ID, new org.apache.thrift.meta_data.FieldMetaData("buildId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.POINT_ID, new org.apache.thrift.meta_data.FieldMetaData("pointId", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.I32)));
    tmpMap.put(_Fields.ERROR_CODE, new org.apache.thrift.meta_data.FieldMetaData("errorCode", org.apache.thrift.TFieldRequirementType.REQUIRED, 
        new org.apache.thrift.meta_data.EnumMetaData(org.apache.thrift.protocol.TType.ENUM, com.lc.billion.icefire.protocol.constant.PsBuildSearchErrorCode.class)));
    tmpMap.put(_Fields.REWARDS, new org.apache.thrift.meta_data.FieldMetaData("rewards", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.ListMetaData(org.apache.thrift.protocol.TType.LIST, 
            new org.apache.thrift.meta_data.StructMetaData(org.apache.thrift.protocol.TType.STRUCT, com.lc.billion.icefire.protocol.structure.PsSimpleItem.class))));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(GcBuildSearchResult.class, metaDataMap);
  }

  public GcBuildSearchResult() {
  }

  public GcBuildSearchResult(
    int buildId,
    int pointId,
    com.lc.billion.icefire.protocol.constant.PsBuildSearchErrorCode errorCode)
  {
    this();
    this.buildId = buildId;
    setBuildIdIsSet(true);
    this.pointId = pointId;
    setPointIdIsSet(true);
    this.errorCode = errorCode;
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public GcBuildSearchResult(GcBuildSearchResult other) {
    __isset_bitfield = other.__isset_bitfield;
    this.buildId = other.buildId;
    this.pointId = other.pointId;
    if (other.isSetErrorCode()) {
      this.errorCode = other.errorCode;
    }
    if (other.isSetRewards()) {
      java.util.List<com.lc.billion.icefire.protocol.structure.PsSimpleItem> __this__rewards = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsSimpleItem>(other.rewards.size());
      for (com.lc.billion.icefire.protocol.structure.PsSimpleItem other_element : other.rewards) {
        __this__rewards.add(new com.lc.billion.icefire.protocol.structure.PsSimpleItem(other_element));
      }
      this.rewards = __this__rewards;
    }
  }

  public GcBuildSearchResult deepCopy() {
    return new GcBuildSearchResult(this);
  }

  @Override
  public void clear() {
    setBuildIdIsSet(false);
    this.buildId = 0;
    setPointIdIsSet(false);
    this.pointId = 0;
    this.errorCode = null;
    this.rewards = null;
  }

  /**
   * 建筑ID
   */
  public int getBuildId() {
    return this.buildId;
  }

  /**
   * 建筑ID
   */
  public GcBuildSearchResult setBuildId(int buildId) {
    this.buildId = buildId;
    setBuildIdIsSet(true);
    return this;
  }

  public void unsetBuildId() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __BUILDID_ISSET_ID);
  }

  /** Returns true if field buildId is set (has been assigned a value) and false otherwise */
  public boolean isSetBuildId() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __BUILDID_ISSET_ID);
  }

  public void setBuildIdIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __BUILDID_ISSET_ID, value);
  }

  /**
   * 探索点ID
   */
  public int getPointId() {
    return this.pointId;
  }

  /**
   * 探索点ID
   */
  public GcBuildSearchResult setPointId(int pointId) {
    this.pointId = pointId;
    setPointIdIsSet(true);
    return this;
  }

  public void unsetPointId() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __POINTID_ISSET_ID);
  }

  /** Returns true if field pointId is set (has been assigned a value) and false otherwise */
  public boolean isSetPointId() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __POINTID_ISSET_ID);
  }

  public void setPointIdIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __POINTID_ISSET_ID, value);
  }

  /**
   * 探索结果
   * 
   * @see com.lc.billion.icefire.protocol.constant.PsBuildSearchErrorCode
   */
  @org.apache.thrift.annotation.Nullable
  public com.lc.billion.icefire.protocol.constant.PsBuildSearchErrorCode getErrorCode() {
    return this.errorCode;
  }

  /**
   * 探索结果
   * 
   * @see com.lc.billion.icefire.protocol.constant.PsBuildSearchErrorCode
   */
  public GcBuildSearchResult setErrorCode(@org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.constant.PsBuildSearchErrorCode errorCode) {
    this.errorCode = errorCode;
    return this;
  }

  public void unsetErrorCode() {
    this.errorCode = null;
  }

  /** Returns true if field errorCode is set (has been assigned a value) and false otherwise */
  public boolean isSetErrorCode() {
    return this.errorCode != null;
  }

  public void setErrorCodeIsSet(boolean value) {
    if (!value) {
      this.errorCode = null;
    }
  }

  public int getRewardsSize() {
    return (this.rewards == null) ? 0 : this.rewards.size();
  }

  @org.apache.thrift.annotation.Nullable
  public java.util.Iterator<com.lc.billion.icefire.protocol.structure.PsSimpleItem> getRewardsIterator() {
    return (this.rewards == null) ? null : this.rewards.iterator();
  }

  public void addToRewards(com.lc.billion.icefire.protocol.structure.PsSimpleItem elem) {
    if (this.rewards == null) {
      this.rewards = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsSimpleItem>();
    }
    this.rewards.add(elem);
  }

  /**
   * 探索奖励
   */
  @org.apache.thrift.annotation.Nullable
  public java.util.List<com.lc.billion.icefire.protocol.structure.PsSimpleItem> getRewards() {
    return this.rewards;
  }

  /**
   * 探索奖励
   */
  public GcBuildSearchResult setRewards(@org.apache.thrift.annotation.Nullable java.util.List<com.lc.billion.icefire.protocol.structure.PsSimpleItem> rewards) {
    this.rewards = rewards;
    return this;
  }

  public void unsetRewards() {
    this.rewards = null;
  }

  /** Returns true if field rewards is set (has been assigned a value) and false otherwise */
  public boolean isSetRewards() {
    return this.rewards != null;
  }

  public void setRewardsIsSet(boolean value) {
    if (!value) {
      this.rewards = null;
    }
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case BUILD_ID:
      if (value == null) {
        unsetBuildId();
      } else {
        setBuildId((java.lang.Integer)value);
      }
      break;

    case POINT_ID:
      if (value == null) {
        unsetPointId();
      } else {
        setPointId((java.lang.Integer)value);
      }
      break;

    case ERROR_CODE:
      if (value == null) {
        unsetErrorCode();
      } else {
        setErrorCode((com.lc.billion.icefire.protocol.constant.PsBuildSearchErrorCode)value);
      }
      break;

    case REWARDS:
      if (value == null) {
        unsetRewards();
      } else {
        setRewards((java.util.List<com.lc.billion.icefire.protocol.structure.PsSimpleItem>)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case BUILD_ID:
      return getBuildId();

    case POINT_ID:
      return getPointId();

    case ERROR_CODE:
      return getErrorCode();

    case REWARDS:
      return getRewards();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case BUILD_ID:
      return isSetBuildId();
    case POINT_ID:
      return isSetPointId();
    case ERROR_CODE:
      return isSetErrorCode();
    case REWARDS:
      return isSetRewards();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof GcBuildSearchResult)
      return this.equals((GcBuildSearchResult)that);
    return false;
  }

  public boolean equals(GcBuildSearchResult that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_buildId = true;
    boolean that_present_buildId = true;
    if (this_present_buildId || that_present_buildId) {
      if (!(this_present_buildId && that_present_buildId))
        return false;
      if (this.buildId != that.buildId)
        return false;
    }

    boolean this_present_pointId = true;
    boolean that_present_pointId = true;
    if (this_present_pointId || that_present_pointId) {
      if (!(this_present_pointId && that_present_pointId))
        return false;
      if (this.pointId != that.pointId)
        return false;
    }

    boolean this_present_errorCode = true && this.isSetErrorCode();
    boolean that_present_errorCode = true && that.isSetErrorCode();
    if (this_present_errorCode || that_present_errorCode) {
      if (!(this_present_errorCode && that_present_errorCode))
        return false;
      if (!this.errorCode.equals(that.errorCode))
        return false;
    }

    boolean this_present_rewards = true && this.isSetRewards();
    boolean that_present_rewards = true && that.isSetRewards();
    if (this_present_rewards || that_present_rewards) {
      if (!(this_present_rewards && that_present_rewards))
        return false;
      if (!this.rewards.equals(that.rewards))
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + buildId;

    hashCode = hashCode * 8191 + pointId;

    hashCode = hashCode * 8191 + ((isSetErrorCode()) ? 131071 : 524287);
    if (isSetErrorCode())
      hashCode = hashCode * 8191 + errorCode.getValue();

    hashCode = hashCode * 8191 + ((isSetRewards()) ? 131071 : 524287);
    if (isSetRewards())
      hashCode = hashCode * 8191 + rewards.hashCode();

    return hashCode;
  }

  @Override
  public int compareTo(GcBuildSearchResult other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetBuildId(), other.isSetBuildId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetBuildId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.buildId, other.buildId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetPointId(), other.isSetPointId());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetPointId()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.pointId, other.pointId);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetErrorCode(), other.isSetErrorCode());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetErrorCode()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.errorCode, other.errorCode);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetRewards(), other.isSetRewards());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetRewards()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.rewards, other.rewards);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("GcBuildSearchResult(");
    boolean first = true;

    sb.append("buildId:");
    sb.append(this.buildId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("pointId:");
    sb.append(this.pointId);
    first = false;
    if (!first) sb.append(", ");
    sb.append("errorCode:");
    if (this.errorCode == null) {
      sb.append("null");
    } else {
      sb.append(this.errorCode);
    }
    first = false;
    if (isSetRewards()) {
      if (!first) sb.append(", ");
      sb.append("rewards:");
      if (this.rewards == null) {
        sb.append("null");
      } else {
        sb.append(this.rewards);
      }
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // alas, we cannot check 'buildId' because it's a primitive and you chose the non-beans generator.
    // alas, we cannot check 'pointId' because it's a primitive and you chose the non-beans generator.
    if (errorCode == null) {
      throw new org.apache.thrift.protocol.TProtocolException("Required field 'errorCode' was not present! Struct: " + toString());
    }
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class GcBuildSearchResultStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcBuildSearchResultStandardScheme getScheme() {
      return new GcBuildSearchResultStandardScheme();
    }
  }

  private static class GcBuildSearchResultStandardScheme extends org.apache.thrift.scheme.StandardScheme<GcBuildSearchResult> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, GcBuildSearchResult struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // BUILD_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.buildId = iprot.readI32();
              struct.setBuildIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // POINT_ID
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.pointId = iprot.readI32();
              struct.setPointIdIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 3: // ERROR_CODE
            if (schemeField.type == org.apache.thrift.protocol.TType.I32) {
              struct.errorCode = com.lc.billion.icefire.protocol.constant.PsBuildSearchErrorCode.findByValue(iprot.readI32());
              struct.setErrorCodeIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 4: // REWARDS
            if (schemeField.type == org.apache.thrift.protocol.TType.LIST) {
              {
                org.apache.thrift.protocol.TList _list0 = iprot.readListBegin();
                struct.rewards = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsSimpleItem>(_list0.size);
                @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.structure.PsSimpleItem _elem1;
                for (int _i2 = 0; _i2 < _list0.size; ++_i2)
                {
                  _elem1 = new com.lc.billion.icefire.protocol.structure.PsSimpleItem();
                  _elem1.read(iprot);
                  struct.rewards.add(_elem1);
                }
                iprot.readListEnd();
              }
              struct.setRewardsIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      if (!struct.isSetBuildId()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'buildId' was not found in serialized data! Struct: " + toString());
      }
      if (!struct.isSetPointId()) {
        throw new org.apache.thrift.protocol.TProtocolException("Required field 'pointId' was not found in serialized data! Struct: " + toString());
      }
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, GcBuildSearchResult struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      oprot.writeFieldBegin(BUILD_ID_FIELD_DESC);
      oprot.writeI32(struct.buildId);
      oprot.writeFieldEnd();
      oprot.writeFieldBegin(POINT_ID_FIELD_DESC);
      oprot.writeI32(struct.pointId);
      oprot.writeFieldEnd();
      if (struct.errorCode != null) {
        oprot.writeFieldBegin(ERROR_CODE_FIELD_DESC);
        oprot.writeI32(struct.errorCode.getValue());
        oprot.writeFieldEnd();
      }
      if (struct.rewards != null) {
        if (struct.isSetRewards()) {
          oprot.writeFieldBegin(REWARDS_FIELD_DESC);
          {
            oprot.writeListBegin(new org.apache.thrift.protocol.TList(org.apache.thrift.protocol.TType.STRUCT, struct.rewards.size()));
            for (com.lc.billion.icefire.protocol.structure.PsSimpleItem _iter3 : struct.rewards)
            {
              _iter3.write(oprot);
            }
            oprot.writeListEnd();
          }
          oprot.writeFieldEnd();
        }
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class GcBuildSearchResultTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public GcBuildSearchResultTupleScheme getScheme() {
      return new GcBuildSearchResultTupleScheme();
    }
  }

  private static class GcBuildSearchResultTupleScheme extends org.apache.thrift.scheme.TupleScheme<GcBuildSearchResult> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, GcBuildSearchResult struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      oprot.writeI32(struct.buildId);
      oprot.writeI32(struct.pointId);
      oprot.writeI32(struct.errorCode.getValue());
      java.util.BitSet optionals = new java.util.BitSet();
      if (struct.isSetRewards()) {
        optionals.set(0);
      }
      oprot.writeBitSet(optionals, 1);
      if (struct.isSetRewards()) {
        {
          oprot.writeI32(struct.rewards.size());
          for (com.lc.billion.icefire.protocol.structure.PsSimpleItem _iter4 : struct.rewards)
          {
            _iter4.write(oprot);
          }
        }
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, GcBuildSearchResult struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      struct.buildId = iprot.readI32();
      struct.setBuildIdIsSet(true);
      struct.pointId = iprot.readI32();
      struct.setPointIdIsSet(true);
      struct.errorCode = com.lc.billion.icefire.protocol.constant.PsBuildSearchErrorCode.findByValue(iprot.readI32());
      struct.setErrorCodeIsSet(true);
      java.util.BitSet incoming = iprot.readBitSet(1);
      if (incoming.get(0)) {
        {
          org.apache.thrift.protocol.TList _list5 = iprot.readListBegin(org.apache.thrift.protocol.TType.STRUCT);
          struct.rewards = new java.util.ArrayList<com.lc.billion.icefire.protocol.structure.PsSimpleItem>(_list5.size);
          @org.apache.thrift.annotation.Nullable com.lc.billion.icefire.protocol.structure.PsSimpleItem _elem6;
          for (int _i7 = 0; _i7 < _list5.size; ++_i7)
          {
            _elem6 = new com.lc.billion.icefire.protocol.structure.PsSimpleItem();
            _elem6.read(iprot);
            struct.rewards.add(_elem6);
          }
        }
        struct.setRewardsIsSet(true);
      }
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

