/**
 * Autogenerated by Thrift Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol.constant;


@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public enum PsWatchTowerArmyType implements org.apache.thrift.TEnum {
  /**
   * 玩家PVP
   */
  Player(1),
  /**
   * 黑骑士攻城
   */
  DARK_KNIGHT(2),
  /**
   * 集结进攻
   */
  GATHER_ATTACK(3),
  /**
   * 玩家侦查
   */
  DETECT(4),
  /**
   * 盟友援军
   */
  REINFORCE(5);

  private final int value;

  private PsWatchTowerArmyType(int value) {
    this.value = value;
  }

  /**
   * Get the integer value of this enum value, as defined in the Thrift IDL.
   */
  public int getValue() {
    return value;
  }

  /**
   * Find a the enum type by its integer value, as defined in the Thrift IDL.
   * @return null if the value is not found.
   */
  @org.apache.thrift.annotation.Nullable
  public static PsWatchTowerArmyType findByValue(int value) { 
    switch (value) {
      case 1:
        return Player;
      case 2:
        return DARK_KNIGHT;
      case 3:
        return GATHER_ATTACK;
      case 4:
        return DETECT;
      case 5:
        return REINFORCE;
      default:
        return null;
    }
  }
}
