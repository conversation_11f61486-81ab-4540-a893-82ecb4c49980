/**
 * Autogenerated by <PERSON>hr<PERSON> Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol;

@SuppressWarnings({"cast", "rawtypes", "serial", "unchecked", "unused"})
/**
 * 请求随机迁城
 * @Message(330)
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public class CgCityRandomMove implements org.apache.thrift.TBase<CgCityRandomMove, CgCityRandomMove._Fields>, java.io.Serializable, Cloneable, Comparable<CgCityRandomMove> {
  private static final org.apache.thrift.protocol.TStruct STRUCT_DESC = new org.apache.thrift.protocol.TStruct("CgCityRandomMove");

  private static final org.apache.thrift.protocol.TField MOVE_TO_ALLIANCE_LEADER_FIELD_DESC = new org.apache.thrift.protocol.TField("moveToAllianceLeader", org.apache.thrift.protocol.TType.BOOL, (short)1);
  private static final org.apache.thrift.protocol.TField USE_DIAMOND_FIELD_DESC = new org.apache.thrift.protocol.TField("useDiamond", org.apache.thrift.protocol.TType.BOOL, (short)2);

  private static final org.apache.thrift.scheme.SchemeFactory STANDARD_SCHEME_FACTORY = new CgCityRandomMoveStandardSchemeFactory();
  private static final org.apache.thrift.scheme.SchemeFactory TUPLE_SCHEME_FACTORY = new CgCityRandomMoveTupleSchemeFactory();

  /**
   * 是否随机到盟主身边
   */
  public boolean moveToAllianceLeader; // optional
  /**
   * 盟迁是否消耗钻石
   */
  public boolean useDiamond; // optional

  /** The set of fields this struct contains, along with convenience methods for finding and manipulating them. */
  public enum _Fields implements org.apache.thrift.TFieldIdEnum {
    /**
     * 是否随机到盟主身边
     */
    MOVE_TO_ALLIANCE_LEADER((short)1, "moveToAllianceLeader"),
    /**
     * 盟迁是否消耗钻石
     */
    USE_DIAMOND((short)2, "useDiamond");

    private static final java.util.Map<java.lang.String, _Fields> byName = new java.util.HashMap<java.lang.String, _Fields>();

    static {
      for (_Fields field : java.util.EnumSet.allOf(_Fields.class)) {
        byName.put(field.getFieldName(), field);
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByThriftId(int fieldId) {
      switch(fieldId) {
        case 1: // MOVE_TO_ALLIANCE_LEADER
          return MOVE_TO_ALLIANCE_LEADER;
        case 2: // USE_DIAMOND
          return USE_DIAMOND;
        default:
          return null;
      }
    }

    /**
     * Find the _Fields constant that matches fieldId, throwing an exception
     * if it is not found.
     */
    public static _Fields findByThriftIdOrThrow(int fieldId) {
      _Fields fields = findByThriftId(fieldId);
      if (fields == null) throw new java.lang.IllegalArgumentException("Field " + fieldId + " doesn't exist!");
      return fields;
    }

    /**
     * Find the _Fields constant that matches name, or null if its not found.
     */
    @org.apache.thrift.annotation.Nullable
    public static _Fields findByName(java.lang.String name) {
      return byName.get(name);
    }

    private final short _thriftId;
    private final java.lang.String _fieldName;

    _Fields(short thriftId, java.lang.String fieldName) {
      _thriftId = thriftId;
      _fieldName = fieldName;
    }

    public short getThriftFieldId() {
      return _thriftId;
    }

    public java.lang.String getFieldName() {
      return _fieldName;
    }
  }

  // isset id assignments
  private static final int __MOVETOALLIANCELEADER_ISSET_ID = 0;
  private static final int __USEDIAMOND_ISSET_ID = 1;
  private byte __isset_bitfield = 0;
  private static final _Fields optionals[] = {_Fields.MOVE_TO_ALLIANCE_LEADER,_Fields.USE_DIAMOND};
  public static final java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> metaDataMap;
  static {
    java.util.Map<_Fields, org.apache.thrift.meta_data.FieldMetaData> tmpMap = new java.util.EnumMap<_Fields, org.apache.thrift.meta_data.FieldMetaData>(_Fields.class);
    tmpMap.put(_Fields.MOVE_TO_ALLIANCE_LEADER, new org.apache.thrift.meta_data.FieldMetaData("moveToAllianceLeader", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.BOOL)));
    tmpMap.put(_Fields.USE_DIAMOND, new org.apache.thrift.meta_data.FieldMetaData("useDiamond", org.apache.thrift.TFieldRequirementType.OPTIONAL, 
        new org.apache.thrift.meta_data.FieldValueMetaData(org.apache.thrift.protocol.TType.BOOL)));
    metaDataMap = java.util.Collections.unmodifiableMap(tmpMap);
    org.apache.thrift.meta_data.FieldMetaData.addStructMetaDataMap(CgCityRandomMove.class, metaDataMap);
  }

  public CgCityRandomMove() {
  }

  /**
   * Performs a deep copy on <i>other</i>.
   */
  public CgCityRandomMove(CgCityRandomMove other) {
    __isset_bitfield = other.__isset_bitfield;
    this.moveToAllianceLeader = other.moveToAllianceLeader;
    this.useDiamond = other.useDiamond;
  }

  public CgCityRandomMove deepCopy() {
    return new CgCityRandomMove(this);
  }

  @Override
  public void clear() {
    setMoveToAllianceLeaderIsSet(false);
    this.moveToAllianceLeader = false;
    setUseDiamondIsSet(false);
    this.useDiamond = false;
  }

  /**
   * 是否随机到盟主身边
   */
  public boolean isMoveToAllianceLeader() {
    return this.moveToAllianceLeader;
  }

  /**
   * 是否随机到盟主身边
   */
  public CgCityRandomMove setMoveToAllianceLeader(boolean moveToAllianceLeader) {
    this.moveToAllianceLeader = moveToAllianceLeader;
    setMoveToAllianceLeaderIsSet(true);
    return this;
  }

  public void unsetMoveToAllianceLeader() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __MOVETOALLIANCELEADER_ISSET_ID);
  }

  /** Returns true if field moveToAllianceLeader is set (has been assigned a value) and false otherwise */
  public boolean isSetMoveToAllianceLeader() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __MOVETOALLIANCELEADER_ISSET_ID);
  }

  public void setMoveToAllianceLeaderIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __MOVETOALLIANCELEADER_ISSET_ID, value);
  }

  /**
   * 盟迁是否消耗钻石
   */
  public boolean isUseDiamond() {
    return this.useDiamond;
  }

  /**
   * 盟迁是否消耗钻石
   */
  public CgCityRandomMove setUseDiamond(boolean useDiamond) {
    this.useDiamond = useDiamond;
    setUseDiamondIsSet(true);
    return this;
  }

  public void unsetUseDiamond() {
    __isset_bitfield = org.apache.thrift.EncodingUtils.clearBit(__isset_bitfield, __USEDIAMOND_ISSET_ID);
  }

  /** Returns true if field useDiamond is set (has been assigned a value) and false otherwise */
  public boolean isSetUseDiamond() {
    return org.apache.thrift.EncodingUtils.testBit(__isset_bitfield, __USEDIAMOND_ISSET_ID);
  }

  public void setUseDiamondIsSet(boolean value) {
    __isset_bitfield = org.apache.thrift.EncodingUtils.setBit(__isset_bitfield, __USEDIAMOND_ISSET_ID, value);
  }

  public void setFieldValue(_Fields field, @org.apache.thrift.annotation.Nullable java.lang.Object value) {
    switch (field) {
    case MOVE_TO_ALLIANCE_LEADER:
      if (value == null) {
        unsetMoveToAllianceLeader();
      } else {
        setMoveToAllianceLeader((java.lang.Boolean)value);
      }
      break;

    case USE_DIAMOND:
      if (value == null) {
        unsetUseDiamond();
      } else {
        setUseDiamond((java.lang.Boolean)value);
      }
      break;

    }
  }

  @org.apache.thrift.annotation.Nullable
  public java.lang.Object getFieldValue(_Fields field) {
    switch (field) {
    case MOVE_TO_ALLIANCE_LEADER:
      return isMoveToAllianceLeader();

    case USE_DIAMOND:
      return isUseDiamond();

    }
    throw new java.lang.IllegalStateException();
  }

  /** Returns true if field corresponding to fieldID is set (has been assigned a value) and false otherwise */
  public boolean isSet(_Fields field) {
    if (field == null) {
      throw new java.lang.IllegalArgumentException();
    }

    switch (field) {
    case MOVE_TO_ALLIANCE_LEADER:
      return isSetMoveToAllianceLeader();
    case USE_DIAMOND:
      return isSetUseDiamond();
    }
    throw new java.lang.IllegalStateException();
  }

  @Override
  public boolean equals(java.lang.Object that) {
    if (that instanceof CgCityRandomMove)
      return this.equals((CgCityRandomMove)that);
    return false;
  }

  public boolean equals(CgCityRandomMove that) {
    if (that == null)
      return false;
    if (this == that)
      return true;

    boolean this_present_moveToAllianceLeader = true && this.isSetMoveToAllianceLeader();
    boolean that_present_moveToAllianceLeader = true && that.isSetMoveToAllianceLeader();
    if (this_present_moveToAllianceLeader || that_present_moveToAllianceLeader) {
      if (!(this_present_moveToAllianceLeader && that_present_moveToAllianceLeader))
        return false;
      if (this.moveToAllianceLeader != that.moveToAllianceLeader)
        return false;
    }

    boolean this_present_useDiamond = true && this.isSetUseDiamond();
    boolean that_present_useDiamond = true && that.isSetUseDiamond();
    if (this_present_useDiamond || that_present_useDiamond) {
      if (!(this_present_useDiamond && that_present_useDiamond))
        return false;
      if (this.useDiamond != that.useDiamond)
        return false;
    }

    return true;
  }

  @Override
  public int hashCode() {
    int hashCode = 1;

    hashCode = hashCode * 8191 + ((isSetMoveToAllianceLeader()) ? 131071 : 524287);
    if (isSetMoveToAllianceLeader())
      hashCode = hashCode * 8191 + ((moveToAllianceLeader) ? 131071 : 524287);

    hashCode = hashCode * 8191 + ((isSetUseDiamond()) ? 131071 : 524287);
    if (isSetUseDiamond())
      hashCode = hashCode * 8191 + ((useDiamond) ? 131071 : 524287);

    return hashCode;
  }

  @Override
  public int compareTo(CgCityRandomMove other) {
    if (!getClass().equals(other.getClass())) {
      return getClass().getName().compareTo(other.getClass().getName());
    }

    int lastComparison = 0;

    lastComparison = java.lang.Boolean.compare(isSetMoveToAllianceLeader(), other.isSetMoveToAllianceLeader());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetMoveToAllianceLeader()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.moveToAllianceLeader, other.moveToAllianceLeader);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    lastComparison = java.lang.Boolean.compare(isSetUseDiamond(), other.isSetUseDiamond());
    if (lastComparison != 0) {
      return lastComparison;
    }
    if (isSetUseDiamond()) {
      lastComparison = org.apache.thrift.TBaseHelper.compareTo(this.useDiamond, other.useDiamond);
      if (lastComparison != 0) {
        return lastComparison;
      }
    }
    return 0;
  }

  @org.apache.thrift.annotation.Nullable
  public _Fields fieldForId(int fieldId) {
    return _Fields.findByThriftId(fieldId);
  }

  public void read(org.apache.thrift.protocol.TProtocol iprot) throws org.apache.thrift.TException {
    scheme(iprot).read(iprot, this);
  }

  public void write(org.apache.thrift.protocol.TProtocol oprot) throws org.apache.thrift.TException {
    scheme(oprot).write(oprot, this);
  }

  @Override
  public java.lang.String toString() {
    java.lang.StringBuilder sb = new java.lang.StringBuilder("CgCityRandomMove(");
    boolean first = true;

    if (isSetMoveToAllianceLeader()) {
      sb.append("moveToAllianceLeader:");
      sb.append(this.moveToAllianceLeader);
      first = false;
    }
    if (isSetUseDiamond()) {
      if (!first) sb.append(", ");
      sb.append("useDiamond:");
      sb.append(this.useDiamond);
      first = false;
    }
    sb.append(")");
    return sb.toString();
  }

  public void validate() throws org.apache.thrift.TException {
    // check for required fields
    // check for sub-struct validity
  }

  private void writeObject(java.io.ObjectOutputStream out) throws java.io.IOException {
    try {
      write(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(out)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private void readObject(java.io.ObjectInputStream in) throws java.io.IOException, java.lang.ClassNotFoundException {
    try {
      // it doesn't seem like you should have to do this, but java serialization is wacky, and doesn't call the default constructor.
      __isset_bitfield = 0;
      read(new org.apache.thrift.protocol.TCompactProtocol(new org.apache.thrift.transport.TIOStreamTransport(in)));
    } catch (org.apache.thrift.TException te) {
      throw new java.io.IOException(te);
    }
  }

  private static class CgCityRandomMoveStandardSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public CgCityRandomMoveStandardScheme getScheme() {
      return new CgCityRandomMoveStandardScheme();
    }
  }

  private static class CgCityRandomMoveStandardScheme extends org.apache.thrift.scheme.StandardScheme<CgCityRandomMove> {

    public void read(org.apache.thrift.protocol.TProtocol iprot, CgCityRandomMove struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TField schemeField;
      iprot.readStructBegin();
      while (true)
      {
        schemeField = iprot.readFieldBegin();
        if (schemeField.type == org.apache.thrift.protocol.TType.STOP) { 
          break;
        }
        switch (schemeField.id) {
          case 1: // MOVE_TO_ALLIANCE_LEADER
            if (schemeField.type == org.apache.thrift.protocol.TType.BOOL) {
              struct.moveToAllianceLeader = iprot.readBool();
              struct.setMoveToAllianceLeaderIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          case 2: // USE_DIAMOND
            if (schemeField.type == org.apache.thrift.protocol.TType.BOOL) {
              struct.useDiamond = iprot.readBool();
              struct.setUseDiamondIsSet(true);
            } else { 
              org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
            }
            break;
          default:
            org.apache.thrift.protocol.TProtocolUtil.skip(iprot, schemeField.type);
        }
        iprot.readFieldEnd();
      }
      iprot.readStructEnd();

      // check for required fields of primitive type, which can't be checked in the validate method
      struct.validate();
    }

    public void write(org.apache.thrift.protocol.TProtocol oprot, CgCityRandomMove struct) throws org.apache.thrift.TException {
      struct.validate();

      oprot.writeStructBegin(STRUCT_DESC);
      if (struct.isSetMoveToAllianceLeader()) {
        oprot.writeFieldBegin(MOVE_TO_ALLIANCE_LEADER_FIELD_DESC);
        oprot.writeBool(struct.moveToAllianceLeader);
        oprot.writeFieldEnd();
      }
      if (struct.isSetUseDiamond()) {
        oprot.writeFieldBegin(USE_DIAMOND_FIELD_DESC);
        oprot.writeBool(struct.useDiamond);
        oprot.writeFieldEnd();
      }
      oprot.writeFieldStop();
      oprot.writeStructEnd();
    }

  }

  private static class CgCityRandomMoveTupleSchemeFactory implements org.apache.thrift.scheme.SchemeFactory {
    public CgCityRandomMoveTupleScheme getScheme() {
      return new CgCityRandomMoveTupleScheme();
    }
  }

  private static class CgCityRandomMoveTupleScheme extends org.apache.thrift.scheme.TupleScheme<CgCityRandomMove> {

    @Override
    public void write(org.apache.thrift.protocol.TProtocol prot, CgCityRandomMove struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol oprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet optionals = new java.util.BitSet();
      if (struct.isSetMoveToAllianceLeader()) {
        optionals.set(0);
      }
      if (struct.isSetUseDiamond()) {
        optionals.set(1);
      }
      oprot.writeBitSet(optionals, 2);
      if (struct.isSetMoveToAllianceLeader()) {
        oprot.writeBool(struct.moveToAllianceLeader);
      }
      if (struct.isSetUseDiamond()) {
        oprot.writeBool(struct.useDiamond);
      }
    }

    @Override
    public void read(org.apache.thrift.protocol.TProtocol prot, CgCityRandomMove struct) throws org.apache.thrift.TException {
      org.apache.thrift.protocol.TTupleProtocol iprot = (org.apache.thrift.protocol.TTupleProtocol) prot;
      java.util.BitSet incoming = iprot.readBitSet(2);
      if (incoming.get(0)) {
        struct.moveToAllianceLeader = iprot.readBool();
        struct.setMoveToAllianceLeaderIsSet(true);
      }
      if (incoming.get(1)) {
        struct.useDiamond = iprot.readBool();
        struct.setUseDiamondIsSet(true);
      }
    }
  }

  private static <S extends org.apache.thrift.scheme.IScheme> S scheme(org.apache.thrift.protocol.TProtocol proto) {
    return (org.apache.thrift.scheme.StandardScheme.class.equals(proto.getScheme()) ? STANDARD_SCHEME_FACTORY : TUPLE_SCHEME_FACTORY).getScheme();
  }
}

