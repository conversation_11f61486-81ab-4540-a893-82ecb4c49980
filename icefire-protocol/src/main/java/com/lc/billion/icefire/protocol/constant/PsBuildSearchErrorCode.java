/**
 * Autogenerated by Thrift Compiler (0.16.0)
 *
 * DO NOT EDIT UNLESS YOU ARE SURE THAT YOU KNOW WHAT YOU ARE DOING
 *  @generated
 */
package com.lc.billion.icefire.protocol.constant;


/**
 * 建筑内探索错误码
 */
@javax.annotation.Generated(value = "Autogenerated by Thrift Compiler (0.16.0)")
public enum PsBuildSearchErrorCode implements org.apache.thrift.TEnum {
  /**
   * 探索成功
   */
  SUCCESS(1),
  /**
   * 建筑配置不存在
   */
  BUILDING_CONFIG_NOT_EXIST(2),
  /**
   * 点位配置不存在
   */
  POINT_CONFIG_NOT_EXIST(3),
  /**
   * 点位不属于当前建筑
   */
  POINT_NOT_BELONG_BUILDING(4),
  /**
   * 点位已经完成
   */
  POINT_ALREADY_COMPLETED(5),
  /**
   * 点位探索条件不满足
   */
  POINT_CONDITION_NOT_MET(6);

  private final int value;

  private PsBuildSearchErrorCode(int value) {
    this.value = value;
  }

  /**
   * Get the integer value of this enum value, as defined in the Thrift IDL.
   */
  public int getValue() {
    return value;
  }

  /**
   * Find a the enum type by its integer value, as defined in the Thrift IDL.
   * @return null if the value is not found.
   */
  @org.apache.thrift.annotation.Nullable
  public static PsBuildSearchErrorCode findByValue(int value) { 
    switch (value) {
      case 1:
        return SUCCESS;
      case 2:
        return BUILDING_CONFIG_NOT_EXIST;
      case 3:
        return POINT_CONFIG_NOT_EXIST;
      case 4:
        return POINT_NOT_BELONG_BUILDING;
      case 5:
        return POINT_ALREADY_COMPLETED;
      case 6:
        return POINT_CONDITION_NOT_MET;
      default:
        return null;
    }
  }
}
