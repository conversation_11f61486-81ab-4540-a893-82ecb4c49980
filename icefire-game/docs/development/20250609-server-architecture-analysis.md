# ICE WAR SLG游戏服务器架构分析

## 前言

本文档基于对 ICE WAR SLG游戏服务器代码库的深入分析，旨在全面梳理其架构设计、技术栈和关键组件，为团队成员理解系统架构提供详尽的参考资料。

## 项目概览

一款策略类SLG游戏的服务器端实现，采用分层架构设计，以保证开发简单性和线程安全为核心目标。项目使用Java 21开发。

## 整体架构图

```
┌─────────────────────────────────────────────────────────────────┐
│                        客户端 (Client)                            │
└─────────────────┬───────────────────────────────────────────────┘
                  │ WebSocket/HTTP 通信
┌─────────────────▼───────────────────────────────────────────────┐
│                    icefire-web (Web层)                         │
│  ┌─────────────────────────────────────────────────────────────┐│
│  │  Web接口服务 │ 静态资源 │ GM管理 │ 运营数据查询          ││
│  └─────────────────────────────────────────────────────────────┘│
└─────────────────┬───────────────────────────────────────────────┘
                  │ HTTP/RPC调用
┌─────────────────▼───────────────────────────────────────────────┐
│                  icefire-game (游戏核心服务)                     │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │                    网络层 (net)                            │ │
│ │  WebSocket服务器 │ 消息分发器 │ 协议编解码              │ │
│ └───────────────────────┬─────────────────────────────────────┘ │
│                         │                                       │
│ ┌───────────────────────▼─────────────────────────────────────┐ │
│ │                   消息处理层 (msg)                          │ │
│ │  MainWorker │ 消息执行器 │ 消息路由 │ 异步消息发送        │ │
│ └───────────────────────┬─────────────────────────────────────┘ │
│                         │                                       │
│ ┌───────────────────────▼─────────────────────────────────────┐ │
│ │                    业务逻辑层 (biz)                         │ │
│ │ ┌─────────────┬─────────────┬─────────────┬─────────────┐ │ │
│ │ │   服务层     │   管理层     │   数据模型   │   工具类     │ │ │
│ │ │ (service)   │ (manager)   │ (model)    │   (util)    │ │ │
│ │ └─────────────┴─────────────┴─────────────┴─────────────┘ │ │
│ └───────────────────────┬─────────────────────────────────────┘ │
│                         │                                       │
│ ┌───────────────────────▼─────────────────────────────────────┐ │
│ │                    数据访问层 (dao)                         │ │
│ │  MongoDB访问 │ Redis缓存 │ 内存索引 │ 数据持久化           │ │
│ └─────────────────────────────────────────────────────────────┘ │
└─────────────────┬───────────────────────────────────────────────┘
                  │
┌─────────────────▼───────────────────────────────────────────────┐
│                    基础框架层                                    │
│ ┌─────────────────────────────────────────────────────────────┐ │
│ │  icefire-core │ icefire-protocol │ sgf-core │ sgf-net     │ │
│ │  核心工具类    │ 通信协议定义      │ 框架核心  │ 网络框架    │ │
│ └─────────────────────────────────────────────────────────────┘ │
└─────────────────┬───────────────────────────────────────────────┘
                  │
┌─────────────────▼───────────────────────────────────────────────┐
│                    基础设施层                                    │
│  MongoDB数据库 │ Redis缓存 │ ZooKeeper配置 │ Docker容器        │
└─────────────────────────────────────────────────────────────────┘
```

## 模块架构分析

### 1. 项目模块构成

#### 1.1 核心模块
- **icefire-game**: 游戏主服务，包含所有核心业务逻辑
- **icefire-web**: Web管理界面和HTTP接口服务
- **icefire-protocol**: 定义客户端与服务器通信协议
- **icefire-core**: 核心工具类和公共组件

#### 1.2 框架模块
- **sgf-core**: 单线程游戏框架核心
- **sgf-net**: 网络通信框架
- **sgf-common**: 公共工具类

#### 1.3 支撑模块
- **ls-flyway-mongo**: MongoDB数据库版本管理
- **icefire-parent**: Maven父项目配置
- **sgf-parent**: SGF框架父项目配置

### 2. 核心架构设计

#### 2.1 单线程主业务模型

```java
// MainWorker - 核心单线程处理器
public class MainWorker extends SingleThreadTaskWorker<Object> {
    // 所有业务逻辑在此线程中串行执行
    public void put(IMessage msg) {
        addTask(msg);
    }
    
    @Override
    protected void execute(Object task) {
        // 确保所有业务操作的线程安全
        msgExec.execute((IMessage) task);
    }
}
```

**设计原理**:
- 所有核心业务逻辑在`MainWorker`单线程中执行
- 通过消息队列机制实现并发请求的序列化处理
- 从根本上避免并发问题，确保数据一致性

#### 2.2 消息处理架构

```java
// 消息处理流程
Client -> WebSocket -> GcMessageDispatcher -> MainWorker -> MessageHandler -> BusinessService
```

**关键组件**:
- **GcMessageDispatcher**: 消息分发器，决定消息是否需要主线程处理
- **GlobalMessageExecutor**: 全局消息执行器，处理具体业务逻辑
- **CgAbstractMessageHandler**: 抽象消息处理器，提供统一处理模板

#### 2.3 数据模型设计

```java
// 玩家数据模型
public class Role extends AbstractRole implements IPropBean<ICalcProp.RoleType> {
    // 游戏业务数据
    private final Map<Currency, Long> currencies = new HashMap<>();
    
    @JsonIgnore
    private transient Player player; // 网络连接对象
    
    // 属性计算系统
    private transient CalcPropHelper<ICalcProp.RoleType> roleCalcPropHelper;
}

public class Player {
    // 网络连接相关
    private transient NetSession session;
    private transient Role role;
}
```

**设计特点**:
- **Role**: 承载游戏业务数据，持久化到数据库
- **Player**: 承载网络连接信息，运行时临时对象
- **分离关注点**: 业务逻辑与网络通信解耦

### 3. 业务服务架构

#### 3.1 服务层次结构

```
├── biz/service/impl/          # 业务服务实现
│   ├── alliance/              # 联盟系统
│   ├── army/                  # 军队系统  
│   ├── battle/                # 战斗系统
│   ├── building/              # 建筑系统
│   ├── hero/                  # 英雄系统
│   ├── world/                 # 世界管理
│   └── ...                    # 其他业务模块
├── biz/manager/               # 业务管理器
├── biz/model/                 # 数据模型
└── biz/dao/                   # 数据访问层
```

#### 3.2 世界管理系统

```java
public class WorldServiceImpl {
    private World world; // 游戏世界实例
    
    // 世界初始化
    public void init() {
        this.world = new World();
        this.world.init(srvDpd);
    }
    
    // 玩家进入世界
    public void addPlayer(Player player, Point point) {
        Role role = world.getRole(player.getId());
        world.addPlayer(player);
        role.onEnterWorld();
    }
}
```

#### 3.3 调度系统

```java
public class WorldScheduler extends SingleThreadWorker {
    @Override
    protected void execute() throws InterruptedException {
        BizTime.updateTime(); // 更新游戏时间
        
        if (worldWorker.canSchedule()) {
            mainWorker.putWorldWorker(worldWorker); // 提交到主线程执行
        }
        
        Thread.sleep(BizConstants.TICK_PERIOD * 2 / 3);
    }
}
```

### 4. 网络通信架构

#### 4.1 协议设计

```java
// 使用Apache Thrift定义协议
public class CgBuildSearch implements TBase<CgBuildSearch, CgBuildSearch._Fields> {
    private int buildId;    // 建筑ID
    private int pointId;    // 探索点ID
    // Thrift自动生成序列化代码
}
```

#### 4.2 RPC架构

```java
// 跨服务通信使用Thrift RPC
public class RPCServer implements IRPCService {
    private Map<String, Object> handlerMap = new ConcurrentHashMap<>();
    
    public void regService(IRPCService service) {
        // 注册RPC服务
        handlerMap.put(service.getClass().getName(), service);
    }
    
    @Override
    public ByteBuffer call(ByteBuffer request) throws TException {
        // 处理RPC调用
        RequestData requestData = SerializeUtil.deserialize(request);
        Method method = object.getClass().getMethod(requestData.getMethodName(), 
                                                   requestData.getParameterTypes());
        Object result = method.invoke(object, requestData.getParameters());
        return ByteBuffer.wrap(SerializeUtil.serialize(response));
    }
}
```

### 5. 数据持久化架构

#### 5.1 数据访问层设计

```java
// 基础DAO抽象
public abstract class RolesEntityDao<T extends IRolesEntity> extends AbstractEntityDao<T> {
    // 内存缓存
    private final ConcurrentMap<Long, T> entities = new ConcurrentHashMap<>();
    
    // 按玩家ID查询
    protected abstract MongoCursor<T> doFindByPlayerId(int db, Long playerId);
    
    // 内存索引管理
    protected abstract void putMemoryIndexes(T entity);
    protected abstract void removeMemoryIndexes(T entity);
}
```

#### 5.2 数据模型特点

```java
// 实体接口
public interface IRolesEntity {
    Long getRoleId();           // 所属角色ID
    void setRoleId(Long roleId);
}

// 数据实体基类
public abstract class AbstractEntity implements Serializable {
    @JsonIgnore
    private transient int db;           // 数据库分片
    @JsonIgnore  
    private transient int currentServerId; // 当前服务器ID
}
```

## 技术栈分析

### 1. 核心技术

| 技术栈 | 版本 | 用途 |
|--------|------|------|
| Java | 21 | 主要开发语言 |
| Spring Framework | 5.x | 依赖注入和组件管理 |
| Apache Thrift | - | RPC通信和协议定义 |
| MongoDB | - | 主数据库 |
| Redis | - | 缓存和会话存储 |
| ZooKeeper | - | 分布式配置管理 |
| Netty | - | 网络通信框架 |
| Docker | - | 容器化部署 |

### 2. 框架特性

#### 2.1 SGF框架

**SimpleGameFramework (SGF)** 是项目的底层游戏框架：

```java
// 单线程任务处理器
public abstract class SingleThreadTaskWorker<T> implements Flow.Subscriber<T> {
    private SubmissionPublisher<T> subject;
    
    protected void addTask(T task) {
        subject.submit(task); // 提交任务到单线程执行
    }
    
    protected abstract void execute(T task); // 子类实现具体处理逻辑
}
```

#### 2.2 异步处理机制

```java
// 异步消息发送
public class AsyncMessageSender {
    private final AsyncQueuedWorker<EncodedMsg> worker;
    
    public void send(TBase<?, ?> message, NetSession session) {
        EncodedMsg encodedMsg = encode(message);
        worker.add(encodedMsg); // 异步发送
    }
}
```

### 3. 部署架构

#### 3.1 Docker化部署

```dockerfile
# game_Dockerfile_21
FROM openjdk:21-jdk
COPY target/icefire-game.jar app.jar
EXPOSE 8080 9090
ENTRYPOINT ["java","-jar","/app.jar"]
```

#### 3.2 服务器类型

```java
public enum ServerType {
    GAME,           // 游戏主服务器
    GVG_BATTLE,     // 公会战战斗服务器
    GVG_CONTROL,    // 公会战控制服务器
    TVT_BATTLE,     // TVT战斗服务器
    TVT_CONTROL,    // TVT控制服务器
    KVK_SEASON,     // 跨服赛季服务器
    CSA_CONTROL     // 跨服攻城控制服务器
}
```

## 架构优势分析

### 1. 开发简单性

#### 1.1 统一消息处理模板
```java
@Controller
public class CgBuildSearchHandler extends CgAbstractMessageHandler<CgBuildSearch> {
    @Override
    protected void handle(Role role, CgBuildSearch message) {
        // 开发者只需关注业务逻辑，无需考虑线程安全
        boolean success = buildSearchService.doBuildSearch(role, 
                                                          message.getBuildId(), 
                                                          message.getPointId());
        if (success) {
            sendBuildSearchProgressResponse(role, message.getBuildId());
        }
    }
}
```

#### 1.2 Spring依赖注入
```java
@Autowired
private BuildSearchService buildSearchService; // 自动注入业务服务

@Autowired  
private WorldServiceImpl worldService; // 自动注入世界服务
```

### 2. 线程安全保证

#### 2.1 单线程执行模型
- 所有业务逻辑在`MainWorker`单线程中执行
- 天然避免数据竞争和同步问题
- 使用`@MainWorkerThread`注解标识需要主线程执行的代码

#### 2.2 消息队列机制
```java
// 并发请求通过队列序列化处理
public void dispatch(IMessage message, Object context) {
    if (mainWorkerTypeSet.contains(message.getType())) {
        mainWorker.put(message); // 提交到主线程队列
    }
}
```

### 3. 扩展性设计

#### 3.1 模块化架构
- 业务功能按模块划分，便于团队并行开发
- 服务类型分离，支持不同类型服务器独立部署
- 协议版本化，支持客户端兼容性

#### 3.2 数据分片机制
```java
// 支持数据库分片
public abstract class AbstractEntity {
    @JsonIgnore
    private transient int db; // 数据库分片标识
}
```

## 架构挑战与限制

### 1. 性能瓶颈

#### 1.1 单线程限制
- `MainWorker`单线程可能成为高并发场景下的性能瓶颈
- 所有业务操作需要排队等待，可能导致延迟增加

#### 1.2 内存使用
```java
// 大量数据缓存在内存中
private final ConcurrentMap<Long, T> entities = new ConcurrentHashMap<>();
```

### 2. 扩展限制

#### 2.1 横向扩展困难
- `MainWorker`无法多实例并行运行
- 单服务器承载能力有上限

#### 2.2 实时性要求
- 对于强实时性需求的功能，单线程模型可能不够灵活

## 最佳实践总结

### 1. 开发规范

#### 1.1 消息处理器开发
```java
// 继承抽象处理器，专注业务逻辑
public class NewFeatureHandler extends CgAbstractMessageHandler<CgNewFeature> {
    @Override
    protected void handle(Role role, CgNewFeature message) {
        // 1. 参数验证
        // 2. 业务逻辑处理  
        // 3. 响应消息发送
    }
}
```

#### 1.2 业务服务设计
```java
@Service
public class NewFeatureService {
    
    @Autowired
    private NewFeatureDao dao;
    
    @MainWorkerThread // 标识需要主线程执行
    public boolean processNewFeature(Role role, int param) {
        // 业务逻辑实现
        return true;
    }
}
```

### 2. 性能优化

#### 2.1 异步处理
```java
// 非关键路径使用异步处理
asyncMessageSender.send(notificationMessage, player.getSession());
```

#### 2.2 批量操作
```java
// 批量数据库操作减少IO
public void batchUpdateRoles(List<Role> roles) {
    dao.batchSave(roles);
}
```

### 3. 监控与日志

#### 3.1 性能监控
```java
// MainWorker队列监控
public static int getMainWorkerQueueSize() {
    return mainWorkerQueueSize.get();
}

// 慢日志记录
if (totalCost > slowThreshold) {
    SlowLogUtil.requestLog(msg.getName(), totalCost, waitCost, processCost, roleId);
}
```

#### 3.2 错误处理
```java
try {
    // 业务逻辑
} catch (ToClientException tce) {
    player.send(tce.toClientMsg()); // 发送错误消息给客户端
} catch (Exception e) {
    ErrorLogUtil.exceptionLog(e); // 记录系统错误
}
```

## 技术债务与改进建议

### 1. 架构改进

#### 1.1 性能优化
- **读写分离**: 将只读操作分离到独立线程池
- **异步化**: 更多非关键业务逻辑异步处理
- **缓存优化**: 实施更精细的缓存策略

#### 1.2 扩展性增强
- **微服务化**: 将大型单体服务拆分为微服务
- **事件驱动**: 引入事件驱动架构减少耦合
- **流量削峰**: 实施消息队列缓冲高峰流量

### 2. 代码质量

#### 2.1 规范统一
- 统一异常处理机制
- 完善单元测试覆盖
- 标准化日志格式

#### 2.2 文档完善
- API文档自动生成
- 架构决策记录(ADR)
- 运维手册更新

## 总结

冰火SLG游戏服务器架构是一套经过实践验证的游戏服务器解决方案。其核心优势在于：

1. **简单可靠**: 单线程模型从根本上解决了并发安全问题
2. **开发友好**: 统一的消息处理模板降低了开发复杂度  
3. **功能完整**: 涵盖了SLG游戏所需的各种业务模块
4. **部署灵活**: 支持多种服务器类型和Docker化部署

同时也存在一些限制：

1. **性能瓶颈**: 单线程模型在高并发场景下可能成为瓶颈
2. **扩展限制**: 横向扩展能力受限
3. **实时性**: 对强实时性需求支持有限

总体而言，这套架构在**开发效率**和**系统稳定性**之间找到了良好的平衡点，特别适合中小型SLG游戏的快速开发和稳定运营。对于团队来说，理解和掌握这套架构有助于：

- 加快新功能开发速度
- 减少线程安全相关的bug
- 提高系统整体可维护性
- 为未来架构演进提供基础

---

**文档版本**: v1.0  
**创建时间**: 2025年6月9日  
**最后更新**: 2025年6月9日  
**维护者**: 开发团队 