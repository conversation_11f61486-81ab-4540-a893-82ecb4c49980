package com.lc.billion.icefire.game.biz.service.impl.item.function;

import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.game.biz.config.ItemConfig;
import com.lc.billion.icefire.game.biz.manager.RoleManager;
import com.lc.billion.icefire.game.biz.model.item.BagType;
import com.lc.billion.icefire.game.biz.model.item.Item;
import com.lc.billion.icefire.game.biz.model.item.ItemType;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.bilog.BiLogUtil;
import com.lc.billion.icefire.game.biz.service.impl.item.AbstractItemUseFunction;
import com.lc.billion.icefire.protocol.GcUpdateUsableEmoji;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * 付费表情道具
 * <AUTHOR>
 */
@Service
public class PaidEmojiFunction extends AbstractItemUseFunction {
    private static final Logger logger = LoggerFactory.getLogger(PaidEmojiFunction.class);

    @Autowired
    private BiLogUtil biLogUtil;

    @Autowired
    private RoleManager roleManager;

    @Override
    public ItemType[] getTypes() {
        return new ItemType[] { ItemType.PAID_EMOJI };
    }

    @Override
    public boolean check(Role role, Item item, ItemConfig.ItemMeta itemMeta, int count) {
        Item itemInBag = roleItemManager.getItemByMetaId(role, BagType.MAIN, item.getMetaId());
        if (itemInBag == null) {
            // 背包中没有该道具
            return false;
        }
        if (itemInBag.getCount() < count) {
            // 背包中付费表情道具的数量低于使用数量
            return false;
        }
        return true;
    }

    @Override
    public void effect(Role role, Item item, ItemConfig.ItemMeta itemMeta, int count, Object... params) {
        Map<String, Long> usableEmoji = role.getUsableEmoji();

        if (usableEmoji.get(item.getMetaId()) != null && usableEmoji.get(item.getMetaId()) == 0L) {
            // 玩家已经永久解锁该表情
            logger.info("【付费表情】：玩家（roleId = {}）已经永久解锁了表情（emojiId = {}）",
                    role.getRoleId(), item.getMetaId());
        } else {
            String[] emojiParam = itemMeta.getParam1().split(",");
            for (String s : emojiParam) {
                String emojiId = s.substring(0, s.indexOf("|"));
                long expireTime = getExpireTime(Integer.parseInt(s.substring(s.indexOf("|") + 1)),
                        count, usableEmoji.get(emojiId));
                usableEmoji.put(emojiId, expireTime);
                logger.info("【付费表情】：玩家（roleId = {}）解锁了表情（emojiId = {}），过期时间为{}。",
                        role.getRoleId(), emojiId, expireTime);
            }
            role.setUsableEmoji(usableEmoji);
            roleManager.saveRole(role);

            // 向客户端推送更新
            GcUpdateUsableEmoji gcUpdateUsableEmoji = new GcUpdateUsableEmoji();
            gcUpdateUsableEmoji.setUsableEmoji(usableEmoji);
            role.send(gcUpdateUsableEmoji);

            // BI打点
            biLogUtil.useEmojiItem(role, item.getMetaId(), count);
        }
    }

    /**
     * 计算玩家付费表情的过期时间
     * @param paramTime 付费表情的配置信息中配置的有效时间
     * @param count 玩家使用掉的道具卡数量
     * @param expireTime 该付费表情当前的过期时间，如果是null表示玩家未解锁该付费表情
     * @return 付费表情的新过期时间
     */
    private long getExpireTime(long paramTime, int count, Long expireTime) {
        if (paramTime == 0) {
            // 玩家使用的是永久道具，因此过期时间直接设置为0
            return 0L;
        }
        if (expireTime == null || expireTime < TimeUtil.getNow()) {
            // 玩家未解锁过该表情或表情已经过期，新的过期时间从当前开始计算
            expireTime = TimeUtil.getNow();
        }
        return expireTime + paramTime * 1000L * count;
    }
}
