package com.lc.billion.icefire.game.biz.service.impl.activity.handler.mission;

import com.google.common.collect.Lists;
import com.lc.billion.icefire.core.common.TimeUtil;
import com.lc.billion.icefire.game.biz.config.ActivityListConfig;
import com.lc.billion.icefire.game.biz.dao.mongo.roles.RoleActivityMissionDao;
import com.lc.billion.icefire.game.biz.model.activity.Activity;
import com.lc.billion.icefire.game.biz.model.activity.mission.ActivityMissionRewardStatus;
import com.lc.billion.icefire.game.biz.model.activity.mission.RoleActivityMission;
import com.lc.billion.icefire.game.biz.model.item.ItemUtils;
import com.lc.billion.icefire.game.biz.model.item.SimpleItem;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.activity.AbstractActivityHandler;
import com.lc.billion.icefire.game.biz.service.impl.functionSwitch.FunctionType;
import com.lc.billion.icefire.game.biz.service.impl.mission.ActivityMissionServiceImpl;
import com.lc.billion.icefire.game.support.LogReasons;
import com.lc.billion.icefire.protocol.GcAcitivityUpdate;
import com.lc.billion.icefire.protocol.GcActivityReceiveReward;
import com.lc.billion.icefire.protocol.constant.PsErrorCode;
import com.lc.billion.icefire.protocol.structure.PsActivityInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName ActivityMissionHandler
 * @Description
 * <AUTHOR>
 * @Date 2024/2/20 16:33
 * @Version 1.0
 */
@Component
public abstract class ActivityMissionHandler<T extends Activity> extends AbstractActivityHandler<T> {
    @Autowired
    private RoleActivityMissionDao roleActivityMissionDao;

    @Autowired
    private ActivityMissionServiceImpl service;


    @Override
    public PsActivityInfo getRoleActivityInfo(Role role, ActivityListConfig.ActivityListMeta activityMeta) {
        Activity activity = activityDao.findActivityByMetaId(activityMeta.getId());
        if (activity != null && !service.isRoleActOver(role, activityMeta.getId(), TimeUtil.getNow())) {
            PsActivityInfo info = new PsActivityInfo();
            info.setId(String.valueOf(activity.getPersistKey()));
            info.setStartTime(role.getCreateTime() + activityMeta.getRoleStartTime());
            long endTime = role.getCreateTime() + activityMeta.getRoleEndTime();
            // 活动结束不给客户端返回
            info.setEndTime(endTime);
            info.setMetaId(activityMeta.getId());
            info.setType(getType().getPsType());
            info.setStatus(activity.getStatus().getPsStatus());
            info.setHasReward(calculateActivityRewardShowForLogin(role, activity) > 0);
            return info;
        }
        return null;
    }


    @Override
    public void receiveReward(Role role, String activityTypeMetaId, String goalMetaId) {
        Activity activity = activityDao.findActivityByActivityType(getType());
        if (activity != null) {
            if (!srvDpd.getFunctionSwitchService().isOpen(FunctionType.ACTIVITY_MISSION.getId(), role)) {
                return;
            }
            ActivityListConfig.ActivityListMeta activityListMeta = configService.getConfig(ActivityListConfig.class).getMetaById(activity.getMetaId());
            RoleActivityMission roleActivityMission = roleActivityMissionDao.getRoleActivityMission(role.getId(), activity.getMetaId());
            if (roleActivityMission == null ||
                    roleActivityMission.getStatus() != ActivityMissionRewardStatus.CLAIMABLE) {
                role.send(wrapperReceiveReward(role, activityTypeMetaId, goalMetaId, activity, null, PsErrorCode.ACTIVITY_RECEIVE_REWARD_ERROR));
                return;
            }

            if (roleActivityMission.getStatus() != ActivityMissionRewardStatus.CLAIMED) {
                role.send(wrapperReceiveReward(role, activityTypeMetaId, goalMetaId, activity, null, PsErrorCode.ACTIVITY_REWARD_ALREADY_RECEIVED));
                return;
            }

            // 领取奖励
            String rewardId = activityListMeta.getRewordShow();
            List<SimpleItem> items = Lists.newArrayList();
            if (rewardId != null) {
                items = dropService.drop(rewardId);
            }

            roleActivityMission.setStatus(ActivityMissionRewardStatus.CLAIMED);
            roleActivityMissionDao.save(roleActivityMission);

            itemService.give(role, items, LogReasons.ItemLogReason.NEW_HERO_POWER_REVIEW_REWARD);
            role.send(wrapperReceiveReward(role, activityTypeMetaId, goalMetaId, activity, items, PsErrorCode.SUCCESS));

            PsActivityInfo psActivityInfo = getRoleActivityInfo(role, activityListMeta);
            if (psActivityInfo != null) {
                role.send(new GcAcitivityUpdate(psActivityInfo));
            } 
        }
    }

    private GcActivityReceiveReward wrapperReceiveReward(Role role, String activityTypeMetaId, String goalMetaId,
                                                         Activity activity, List<SimpleItem> itemList,
                                                         PsErrorCode code) {
        GcActivityReceiveReward msg = new GcActivityReceiveReward();
        msg.setActivityId(activity.getMetaId());
        msg.setActivityTypeMetaId(activityTypeMetaId);
        msg.setGoalSn(goalMetaId == null ? "" : goalMetaId);
        msg.setCode(code);
        msg.setItems(itemList == null ? new ArrayList<>() : ItemUtils.toPsSimpleItem(itemList));
        return msg;
    }

    @Override
    public Long getActivityStartTime(Activity activity,ActivityListConfig.ActivityListMeta meta, Role role) {
        return role.getCreateTime() + meta.getRoleStartTime();
    }

    @Override
    public Long getActivityEndTime(Activity activity,ActivityListConfig.ActivityListMeta meta, Role role) {
        return role.getCreateTime() + meta.getRoleEndTime();
    }
}
