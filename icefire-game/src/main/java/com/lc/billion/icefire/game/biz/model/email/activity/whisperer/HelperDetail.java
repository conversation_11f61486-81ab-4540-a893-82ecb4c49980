package com.lc.billion.icefire.game.biz.model.email.activity.whisperer;

import com.lc.billion.icefire.game.biz.model.item.SimpleItem;
import com.lc.billion.icefire.game.biz.model.role.info.DefaultRoleInfo;
import com.lc.billion.icefire.game.biz.model.role.info.RoleInfo;
import com.lc.billion.icefire.protocol.structure.PsActivityWhispererDetailInfo;
import com.lc.billion.icefire.protocol.structure.PsSimpleItem;
import com.simfun.sgf.utils.JavaUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/10/20
 */
public class HelperDetail {
	private String name;
	private String head;
    private RoleInfo roleInfo;
	private String allianceAlias;// 联盟简称
	private List<SimpleItem> rewards; // 得到的奖励

	public HelperDetail() {

	}

	public HelperDetail(String name, String head, RoleInfo roleInfo, String alias, List<SimpleItem> rewards) {
		this.name = name;
		this.head = head;
        this.roleInfo = roleInfo;
		this.allianceAlias = alias;
		this.rewards = rewards;
	}

	protected PsActivityWhispererDetailInfo toInfo() {
		PsActivityWhispererDetailInfo info = new PsActivityWhispererDetailInfo();
		info.setName(name == null ? "" : name);
		info.setHead(head == null ? "" : head);
        info.setRoleInfo(roleInfo == null ? DefaultRoleInfo.toPsRoleInfo() : roleInfo.toPsRoleInfo());
		info.setAllianceAlias(allianceAlias == null ? "" : allianceAlias);
		if (rewards != null) {
			List<PsSimpleItem> list = new ArrayList<>();
			for (SimpleItem item : rewards) {
				PsSimpleItem psItem = new PsSimpleItem();
				psItem.setMetaId(item.getMetaId());
				psItem.setCount(item.getCount());
				list.add(psItem);
			}
			if (JavaUtils.bool(list))
				info.setItems(list);
		}
		return info;
	}
}
