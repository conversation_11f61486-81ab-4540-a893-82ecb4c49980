package com.lc.billion.icefire.game.msg.handler.impl.alliance.base;

import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.alliance.AllianceServiceImpl;
import com.lc.billion.icefire.game.msg.handler.CgAbstractMessageHandler;
import com.lc.billion.icefire.protocol.CgFirstJoinAllianceReward;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

/**
 * @author: 周会勤
 * @create: 2020-03-25 14:14
 **/

@Controller
public class CgFirstJoinAllianceRewardHandler extends CgAbstractMessageHandler<CgFirstJoinAllianceReward> {
    @Autowired
    private AllianceServiceImpl allianceService;

	@Override
	protected void handle(Role role, CgFirstJoinAllianceReward message) {
	    allianceService.receiveFirstJoinAllianceReward(role);
    }
}
