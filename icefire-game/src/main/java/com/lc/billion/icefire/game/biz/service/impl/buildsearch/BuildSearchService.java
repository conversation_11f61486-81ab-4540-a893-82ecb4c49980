package com.lc.billion.icefire.game.biz.service.impl.buildsearch;

import com.lc.billion.icefire.game.biz.config.BuildingSearchBubbleConfig;
import com.lc.billion.icefire.game.biz.config.BuildingSearchConfig;
import com.lc.billion.icefire.game.biz.manager.RoleBuildSearchManager;
import com.lc.billion.icefire.game.biz.model.buildsearch.BuildSearchRewardType;
import com.lc.billion.icefire.game.biz.model.buildsearch.RoleBuildSearchProgress;
import com.lc.billion.icefire.game.biz.model.item.SimpleItem;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.core.config.service.impl.ConfigServiceImpl;
import com.lc.billion.icefire.game.biz.service.impl.item.ItemServiceImpl;
import com.lc.billion.icefire.game.support.LogReasons;
import com.lc.billion.icefire.protocol.GcBuildSearchResult;
import com.lc.billion.icefire.protocol.constant.PsBuildSearchErrorCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;


@Slf4j
@Service
public class BuildSearchService {

    @Autowired
    private RoleBuildSearchManager roleBuildSearchManager;

    @Autowired
    private ConfigServiceImpl configService;

    @Autowired
    private ItemServiceImpl itemService;


    /**
     * 执行建筑探索
     */
    public GcBuildSearchResult doBuildSearch(Role role, int buildId, int pointId) {

        // 获取建筑配置
        var buildConfig = getBuildingSearchConfig().getBuildingSearchMeta(buildId);
        if (buildConfig == null) {
            log.warn("建筑配置不存在: buildId={}", buildId);
            return new GcBuildSearchResult(buildId, pointId, PsBuildSearchErrorCode.BUILDING_CONFIG_NOT_EXIST);
        }

        // 获取点位配置
        var bubbleConfig = getBuildingSearchBubbleConfig().get(String.valueOf(pointId));
        if (bubbleConfig == null) {
            log.warn("点位配置不存在: pointId={}", pointId);
            return new GcBuildSearchResult(buildId, pointId, PsBuildSearchErrorCode.POINT_CONFIG_NOT_EXIST);
        }

        // 检查点位是否属于当前建筑
        if (!buildConfig.getBubbleList().contains(pointId)) {
            log.warn("点位不属于当前建筑: buildId={}, pointId={}", buildId, pointId);
            return new GcBuildSearchResult(buildId, pointId, PsBuildSearchErrorCode.POINT_NOT_BELONG_BUILDING);
        }

        // 获取玩家探索进度
        var progress = roleBuildSearchManager.getRoleBuildSearchProgress(role, buildId);

        // 检查是否已经完成
        if (progress.isPointCompleted(pointId)) {
            log.warn("点位已经完成: roleId={}, buildId={}, pointId={}", role.getId(), buildId, pointId);
            return new GcBuildSearchResult(buildId, pointId, PsBuildSearchErrorCode.POINT_ALREADY_COMPLETED);
        }

        // 检查是否可以探索（检查前置条件）
        if (!canExplore(progress, pointId, bubbleConfig)) {
            log.warn("点位探索条件不满足: roleId={}, buildId={}, pointId={}", role.getId(), buildId, pointId);
            return new GcBuildSearchResult(buildId, pointId, PsBuildSearchErrorCode.POINT_CONDITION_NOT_MET);
        }

        // 标记点位为已完成
        progress.addCompletedPoint(pointId);

        progress.removeAvailablePoint(pointId);
        
        // 开启下一组探索点
        var nextBubbles = bubbleConfig.getNextBubbleList();
        if (nextBubbles != null && !nextBubbles.isEmpty()) {
            for (Integer nextBubbleId : nextBubbles) {
                progress.addAvailablePoint(nextBubbleId);
            }
            log.debug("开启下一组探索点: roleId={}, buildId={}, nextBubbles={}", role.getId(), buildId, nextBubbles);
        }

        Optional<List<SimpleItem>> rewards = Optional.empty();
        // 如果有奖励且尚未获得奖励，则发放奖励
        if (hasReward(bubbleConfig) && !progress.isPointRewarded(pointId)) {
            rewards = giveReward(role, bubbleConfig);
            progress.addRewardedPoint(pointId);
        }

        // 保存进度更新
        roleBuildSearchManager.saveRoleBuildSearch(progress);

        log.debug("建筑探索成功: roleId={}, buildId={}, pointId={}", role.getId(), buildId, pointId);
        
        // 构造成功响应，包含奖励信息
        var result = new GcBuildSearchResult(buildId, pointId, PsBuildSearchErrorCode.SUCCESS);

        // 如果有奖励，设置奖励信息
        rewards.ifPresent(rewardList -> {
            var psRewards = rewardList.stream()
                    .map(SimpleItem::toPsObject)
                    .collect(Collectors.toList());
            result.setRewards(psRewards);
        });
        
        return result;
    }

    /**
     * 检查点位是否有奖励
     */
    private boolean hasReward(BuildingSearchBubbleConfig.BuildingSearchBubbleMeta bubbleConfig) {
        // 检查rewardType是否为BACKEND_REWARD或者有奖励物品
        return bubbleConfig.getRewardType() == BuildSearchRewardType.BACKEND_REWARD &&
               (bubbleConfig.getRewardItems() != null && !bubbleConfig.getRewardItems().isEmpty());
    }

    /**
     * 检查是否可以探索
     */
    private boolean canExplore(RoleBuildSearchProgress progress, int pointId, BuildingSearchBubbleConfig.BuildingSearchBubbleMeta bubbleConfig) {

        // 检查点位是否在可探索列表中
        if (!progress.isPointAvailable(pointId)) {
            log.warn("不在可探索列表中: pointId={}", pointId);
            return false;
        }

        var bubbleShow = bubbleConfig.getBubbleShow();
        
        // 如果没有显示条件或者为"0"，则可以探索
        if (bubbleShow == null || bubbleShow == 0) {
            return true;
        }

        // 检查前置点位是否已完成
        return progress.isPointCompleted(bubbleShow);
    }

    /**
     * 发放奖励
     */
    private Optional<List<SimpleItem>> giveReward(Role role, BuildingSearchBubbleConfig.BuildingSearchBubbleMeta bubbleConfig) {
        var rewards = bubbleConfig.getRewardItems();
        if (rewards != null && !rewards.isEmpty()) {
            itemService.give(role, rewards, LogReasons.ItemLogReason.BUILD_SEARCH_REWARD);
            log.debug("发放建筑探索奖励: roleId={}, bubbleId={}, rewards={}", role.getId(), bubbleConfig.getId(), rewards);
            return Optional.of(rewards);
        }
        return Optional.empty();
    }


    /**
     * 获取建筑探索进度
     */
    public RoleBuildSearchProgress getBuildSearchProgress(Role role, int buildId) {
        // 获取建筑配置
        var buildConfig = getBuildingSearchConfig().getBuildingSearchMeta(buildId);
        if (buildConfig == null) {
            log.warn("建筑配置不存在: buildId={}", buildId);
            return null;
        }

        // 获取玩家探索进度
        var progress = roleBuildSearchManager.getRoleBuildSearchProgress(role, buildId);

        // 初始化探索进度（如果是第一次访问）
        if (progress.getAvailablePoints().isEmpty() && progress.getCompletedPoints().isEmpty()) {
            initBuildSearchProgress(progress, buildConfig);
        }
        return progress;
    }

    /**
     * 初始化建筑探索进度
     */
    private void initBuildSearchProgress(RoleBuildSearchProgress progress, BuildingSearchConfig.BuildingSearchMeta buildConfig) {
        // 找到初始可探索的点位（没有前置条件的点位，或者bubbleShow为空的点位）
        for (var bubbleId : buildConfig.getBubbleList()) {
            var bubbleConfig =  getBuildingSearchBubbleConfig().get(String.valueOf(bubbleId));
            if (bubbleConfig != null) {
                var bubbleShow = bubbleConfig.getBubbleShow();
                // 如果bubbleShow为空或者为"0"，表示是初始可探索的点位
                if (bubbleShow == null || bubbleShow == 0) {
                    progress.addAvailablePoint(bubbleId);
                }
            }
        }
        
        roleBuildSearchManager.saveRoleBuildSearch( progress);
        
        log.debug("初始化建筑探索进度: roleId={}, buildId={}, initialBubbles={}",  progress.getRoleId(), progress.getBuildId(), progress.getAvailablePoints());
    }

    private BuildingSearchConfig getBuildingSearchConfig() {
        return configService.getConfig(BuildingSearchConfig.class);
    }

    private BuildingSearchBubbleConfig getBuildingSearchBubbleConfig() {
        return configService.getConfig(BuildingSearchBubbleConfig.class);
    }
}
