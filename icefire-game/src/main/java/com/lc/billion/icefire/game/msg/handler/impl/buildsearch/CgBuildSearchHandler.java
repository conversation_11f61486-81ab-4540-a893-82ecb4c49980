package com.lc.billion.icefire.game.msg.handler.impl.buildsearch;

import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.buildsearch.BuildSearchService;
import com.lc.billion.icefire.game.msg.handler.CgAbstractMessageHandler;
import com.lc.billion.icefire.protocol.CgBuildSearch;
import com.lc.billion.icefire.protocol.GcBuildSearchProgress;
import com.lc.billion.icefire.protocol.GcBuildSearchResult;
import com.lc.billion.icefire.protocol.constant.PsBuildSearchErrorCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.util.ArrayList;

@Slf4j
@Controller
public class CgBuildSearchHandler extends CgAbstractMessageHandler<CgBuildSearch> {
    
    @Autowired
    private BuildSearchService buildSearchService;

    @Override
    protected void handle(Role role, CgBuildSearch message) {
        try {
            int buildId = message.getBuildId();
            int pointId = message.getPointId();
            
            log.debug("处理建筑探索请求: roleId={}, buildId={}, pointId={}", role.getId(), buildId, pointId);
            
            // 执行建筑探索，获取完整结果
            var searchResult = buildSearchService.doBuildSearch(role, buildId, pointId);
            
            if (searchResult.getErrorCode() == PsBuildSearchErrorCode.SUCCESS) {
                log.debug("建筑探索成功: roleId={}, buildId={}, pointId={}", role.getId(), buildId, pointId);
            } else {
                log.warn("建筑探索失败: roleId={}, buildId={}, pointId={}, errorCode={}", 
                    role.getId(), buildId, pointId, searchResult.getErrorCode());
            }
            
            // 发送响应消息
            sendBuildSearchResponse(role, buildId, searchResult);
            
        } catch (Exception e) {
            log.error("处理建筑探索请求异常: roleId={}, message={}", role.getId(), message, e);
        }
    }
    
    /**
     * 发送建筑探索响应消息
     * @param role 角色
     * @param buildId 建筑ID
     * @param searchResult 探索结果
     */
    private void sendBuildSearchResponse(Role role, int buildId, GcBuildSearchResult searchResult) {
        // 发送进度响应
        var progressData = buildSearchService.getBuildSearchProgress(role, buildId);
        var progressResponse = new GcBuildSearchProgress();
        progressResponse.setBuildId(buildId);
        
        if (progressData != null) {
            progressResponse.setCompletedPointIds(new ArrayList<>(progressData.getCompletedPoints()));
            progressResponse.setLastSearchPointId(progressData.getLastCompletedPoint());
        } else {
            progressResponse.setCompletedPointIds(new ArrayList<>());
            progressResponse.setLastSearchPointId(0);
        }
        
        role.send(progressResponse);
        
        // 发送探索结果响应（已包含奖励信息）
        role.send(searchResult);
    }

}
