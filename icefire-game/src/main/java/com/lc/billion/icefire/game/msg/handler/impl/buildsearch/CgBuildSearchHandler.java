package com.lc.billion.icefire.game.msg.handler.impl.buildsearch;

import com.lc.billion.icefire.game.biz.model.item.SimpleItem;
import com.lc.billion.icefire.game.biz.model.role.Role;
import com.lc.billion.icefire.game.biz.service.impl.buildsearch.BuildSearchService;
import com.lc.billion.icefire.game.msg.handler.CgAbstractMessageHandler;
import com.lc.billion.icefire.protocol.CgBuildSearch;
import com.lc.billion.icefire.protocol.GcBuildSearchProgress;
import com.lc.billion.icefire.protocol.GcBuildSearchResult;
import com.lc.billion.icefire.protocol.constant.PsBuildSearchErrorCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;

import java.util.ArrayList;

@Slf4j
@Controller
public class CgBuildSearchHandler extends CgAbstractMessageHandler<CgBuildSearch> {
    
    @Autowired
    private BuildSearchService buildSearchService;

    @Override
    protected void handle(Role role, CgBuildSearch message) {
        try {
            int buildId = message.getBuildId();
            int pointId = message.getPointId();
            
            log.debug("处理建筑探索请求: roleId={}, buildId={}, pointId={}", role.getId(), buildId, pointId);
            
            // 执行建筑探索
            var code = buildSearchService.doBuildSearch(role, buildId, pointId);
            
            if (code == PsBuildSearchErrorCode.SUCCESS) {
                log.debug("建筑探索成功: roleId={}, buildId={}, pointId={}", role.getId(), buildId, pointId);
                sendBuildSearchProgressResponse(role, buildId, pointId, code);
            } else {
                log.warn("建筑探索失败: roleId={}, buildId={}, pointId={}", role.getId(), buildId, pointId);
                sendBuildSearchProgressResponse(role, buildId, pointId, code);
            }
            
        } catch (Exception e) {
            log.error("处理建筑探索请求异常: roleId={}, message={}", role.getId(), message, e);
        }
    }
    
    /**
     * 发送建筑探索进度响应消息
     * @param role 角色
     * @param buildId 建筑ID
     * @param pointId 探索点ID
     * @param errorCode 错误码
     */
    private void sendBuildSearchProgressResponse(Role role, int buildId, int pointId, PsBuildSearchErrorCode errorCode) {
        var progressData = buildSearchService.getBuildSearchProgress(role, buildId);
        
        // 发送进度响应
        var progressResponse = new GcBuildSearchProgress();
        progressResponse.setBuildId(buildId);
        
        if (progressData != null) {
            progressResponse.setCompletedPointIds(new ArrayList<>(progressData.getCompletedPoints()));
            progressResponse.setLastSearchPointId(progressData.getLastCompletedPoint());
        } else {
            progressResponse.setCompletedPointIds(new ArrayList<>());
            progressResponse.setLastSearchPointId(0);
        }
        
        role.send(progressResponse);
        
        // 发送探索结果响应
        var resultResponse = new GcBuildSearchResult();
        resultResponse.setBuildId(buildId);
        resultResponse.setPointId(pointId);
        resultResponse.setErrorCode(errorCode);
        
        // 如果探索成功且有奖励，设置奖励信息
        if (errorCode == PsBuildSearchErrorCode.SUCCESS) {
            if (buildSearchService.hasPointReward(pointId)) {
                var rewards = buildSearchService.getPointRewards(pointId);
                if (rewards != null && !rewards.isEmpty()) {
                    var psRewards = rewards.stream()
                        .map(SimpleItem::toPsObject)
                        .collect(java.util.stream.Collectors.toList());
                    resultResponse.setRewards(psRewards);
                }
            }
        }
        
        role.send(resultResponse);
    }

}
