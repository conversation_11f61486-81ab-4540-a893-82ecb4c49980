package com.lc.billion.icefire.game.biz.service.impl.rank.impl;

import com.lc.billion.icefire.game.biz.service.impl.rank.RankType;
import org.springframework.stereotype.Service;

@Service
public class RoleDonateWeeklyInAllianceRank extends AbstractPersonalInAllianceScheduleRank {
    @Override
    public RankType getRankType() {
        return RankType.ROLE_DONATE_WEEKLY_ALLIANCE;
    }

    @Override
    public void flush() {
        super.flush();
        for (var alliance : allianceDao.findAll()) {
            if (allianceService.isMirrorAlliance(alliance)) {
                continue;
            }
            for (var member : allianceMemberManager.getMembers(alliance.getId())) {
                var role = roleDao.findById(member.getPersistKey());
                if (role == null) {
                    continue;
                }
                updateRankScore(getRankType(), String.valueOf(role.getPersistKey()), member.getWeekDonate(), 0, alliance.getIdString());
            }
        }
    }
}
