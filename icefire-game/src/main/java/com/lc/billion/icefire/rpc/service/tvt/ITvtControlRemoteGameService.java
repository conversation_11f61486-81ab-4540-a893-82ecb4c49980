package com.lc.billion.icefire.rpc.service.tvt;

import java.util.Collection;
import java.util.List;

import com.lc.billion.icefire.rpc.vo.gvg.ActivityVo;
import com.lc.billion.icefire.rpc.vo.gvg.GVGBattleRecordVo;
import com.lc.billion.icefire.rpc.vo.tvt.TvtBattleServerDispatchRecordVo;
import com.lc.billion.icefire.rpc.vo.tvt.TvtPlayerSignupInfoVo;

/**
 * @author: maoqq
 * @Date: 2022/04/11 11:52 AM
 */
public interface ITvtControlRemoteGameService {
    /**
     * 广播tvt活动
     * @param activity
     */
    void broadcastTvtActivity(ActivityVo activityVo);

    /**
     * 清除战斗服分配记录
     * @param serverId
     */
    void broadcastTvtBattleServerDispatchRecordClear();

    /**
     * 广播战斗服分配记录
     * @param data
     */
    void broadcastTvtBattleServerDispatchRecord(Collection<TvtBattleServerDispatchRecordVo> data);
    /**
     * 广播tvt报名信息
     * @param vo
     */
    void broadcastTvtPlayerSignupInfo(TvtPlayerSignupInfoVo vo);

    void broadcastTvtPlayerSignupInfos(List<TvtPlayerSignupInfoVo> vos);

    /**
     * 广播战斗记录
     */
    String broadcastGVGBattleRecord(GVGBattleRecordVo gvgBattleRecordVo);

    /**
     * 广播跨天重置
     * @param activityVo
     */
    void broadcastTvtActivityDailyReset(ActivityVo activityVo);

    void broadcastTvtPlayerSignupInfoClear();

}
